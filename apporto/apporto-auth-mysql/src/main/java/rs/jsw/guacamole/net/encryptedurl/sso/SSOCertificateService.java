package rs.jsw.guacamole.net.encryptedurl.sso;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Enumeration;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.bouncycastle.openssl.jcajce.JcaPEMWriter;
import org.json.JSONObject;

import com.apporto.hyperstream.core.ApportoProperties;

import lombok.extern.slf4j.Slf4j;

/**
 * Service providing SSO certificate manipulation.
 * 
 * <AUTHOR>
 * 
 */
@Slf4j
public class SSOCertificateService {

    private static final String DEFAULT_SERVER_URL = "http://localhost";
    private static String[] serverURLs = new String[] { DEFAULT_SERVER_URL };
    private static String apiKey = "";

    static {
        try {
            Environment environment = new LocalEnvironment();
            String configuredServers = environment.getProperty(ApportoProperties.SSO_CERT_GEN_SERVER);

            if (configuredServers != null && !configuredServers.trim().isEmpty()) {
                serverURLs = configuredServers.split("\\s*,\\s*");
                logger.info("SSO Certificate generation servers loaded from config: {}", String.join(", ", serverURLs));
            } else {
                logger.warn("SSO_CERT_GEN_SERVER is not defined or is empty. Using default: {}", DEFAULT_SERVER_URL);
            }
        } catch (GuacamoleException e) {
            logger.warn("Failed to load SSO_CERT_GEN_SERVER from environment. Using default: {}", DEFAULT_SERVER_URL, e);
        }

        try {
            Environment environment = new LocalEnvironment();
            String configuredApiKey = environment.getProperty(ApportoProperties.SSO_CERT_GEN_API_KEY);

            if (configuredApiKey != null && !configuredApiKey.trim().isEmpty()) {
                apiKey = configuredApiKey;
                logger.info("SSO Certificate generation API keys loaded from config");
            } else {
                logger.warn("SSO_CERT_GEN_API_KEY is not defined or is empty");
            }
        } catch (GuacamoleException e) {
            logger.warn("Failed to load SSO_CERT_GEN_API_KEY from environment.", e);
        }
    }

    /**
     * Return the full API URL of certificate generation service, added the server URL.
     * 
     * @param API
     * 
     * @return URL contains full API URL.
     * 
     * @throws MalformedURLException
     */
    private static URL buildApiUrl(String baseUrl, String apiPath) throws MalformedURLException {
        String fullUrl = baseUrl.endsWith("/") ? baseUrl : baseUrl + "/";
        fullUrl += apiPath.startsWith("/") ? apiPath.substring(1) : apiPath;
        return new URL(fullUrl);
    }

    /**
     * Call the REST API of certificate generation service to get the PFX certificate.
     * @param connection_type 
     * @param cloud_user 
     * @param conn_id 
     * 
     * @return The byte array of PFX certificate content.
     */
    private static byte[] generatePfxCertificate(String username, String conn_id, String cloud_user, String connection_type) {
        for (String baseUrl : serverURLs) {
            try {
                URL url = buildApiUrl(baseUrl, "/certificate/generate");

                HttpURLConnection connection = (HttpURLConnection)url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("x-api-key", apiKey);
                connection.setDoOutput(true);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("username", username);

                byte[] input = jsonObject.toString().getBytes(StandardCharsets.UTF_8);
                connection.getOutputStream().write(input);

                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    logger.warn("[{}:{}:{}] SSO Cert Gen Server {} returned status {} for user {}", conn_id, cloud_user, connection_type, baseUrl, responseCode, username);
                    continue;
                }

                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line.trim());
                }

                JSONObject responseJson = new JSONObject(response.toString());
                String certificateContent = responseJson.getJSONObject("data").getString("certificate");

                return Base64.getDecoder().decode(certificateContent);

            } catch (Exception e) {
                logger.warn("[{}:{}:{}] SSO Failed to fetch certificate from Cert Gen server: {}", conn_id, cloud_user, connection_type, baseUrl, e);
            }
        }

        logger.error("[{}:{}:{}] All SSO Cert Gen servers failed to provide a certificate for user {}", conn_id, cloud_user, connection_type, username);
        return null;
    }

    /**
     * Convert PFX certificate to PEM formatted certificate and private key.
     * 
     * @return PemCertificate object contains certificate and private key.
     */
    private static PemCertificate convertPfxToPemCertificate(byte[] pfxCertificateBytes, String conn_id, String cloud_user, String connection_type) {
        try {
            String password = "1234";

            // Load PFX file into KeyStore
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            ByteArrayInputStream fis = new ByteArrayInputStream(pfxCertificateBytes);
            keyStore.load(fis, password.toCharArray());

            // Extract keys and certificates
            Enumeration<String> aliases = keyStore.aliases();
            PrivateKey privateKey = null;
            X509Certificate certificate = null;

            while (aliases.hasMoreElements()) {
                String alias = aliases.nextElement();
                if (keyStore.isKeyEntry(alias)) {
                    privateKey = (PrivateKey) keyStore.getKey(alias, password.toCharArray());
                    certificate = (X509Certificate) keyStore.getCertificate(alias);
                    break;
                }
            }

            if (privateKey == null || certificate == null) {
                logger.warn("[{}:{}:{}] SSO Private key or certificate not found in PFX file.", conn_id, cloud_user, connection_type);
                return null;
            }

            StringWriter certificateStringWriter = new StringWriter();
            JcaPEMWriter certificatePemWriter = new JcaPEMWriter(certificateStringWriter);
            certificatePemWriter.writeObject(certificate);
            certificatePemWriter.close();
            certificateStringWriter.close();

            StringWriter privateKeyStringWriter = new StringWriter();
            JcaPEMWriter privateKeyPemWriter = new JcaPEMWriter(privateKeyStringWriter);
            privateKeyPemWriter.writeObject(privateKey);
            privateKeyPemWriter.close();
            privateKeyStringWriter.close();

            PemCertificate res = new PemCertificate();
            res.setCertificate(certificateStringWriter.toString());
            res.setPrivateKey(privateKeyStringWriter.toString());

            return res;
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] SSO Error converting PFX to PEM: " + e.getClass().getSimpleName() + ": " + e.getMessage(), conn_id, cloud_user, connection_type, e);
        }

        return null;
    }

    /**
     * Get the PEM formatted certificate and private key.
     * 
     * @param username
     * @param connection_type 
     * @param cloud_user 
     * @param conn_id 
     * 
     * @return PemCertificate object contains certificate and private key.
     */
    public static PemCertificate getPemCertificate(String username, String conn_id, String cloud_user, String connection_type) {

        byte[] pfxCertificateBytes = generatePfxCertificate(username, conn_id, cloud_user, connection_type);

        if (pfxCertificateBytes == null) {
            logger.error("[{}:{}:{}] SSO Failed to generate PFX certificate for user {}", conn_id, cloud_user, connection_type, username);
            return null;
        }

        PemCertificate pem = convertPfxToPemCertificate(pfxCertificateBytes, conn_id, cloud_user, connection_type);
        if (pem == null) {
            logger.error("[{}:{}:{}] SSO Failed to convert PFX to PEM for user {}", conn_id, cloud_user, connection_type, username);
            return null;
        }

        logger.info("[{}:{}:{}] SSO Successfully generated PEM certificate for user {}", conn_id, cloud_user, connection_type, username);
        return pem;
    }
}
