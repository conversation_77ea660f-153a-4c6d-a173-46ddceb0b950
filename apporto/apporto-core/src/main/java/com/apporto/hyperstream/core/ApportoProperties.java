/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.hyperstream.core;

import org.apache.guacamole.properties.BooleanGuacamoleProperty;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;

/**
 * Properties used for configuring apporto system.
 *
 * <AUTHOR>
 *
 */
public final class ApportoProperties {

    // Properties file params
    public static final StringGuacamoleProperty SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "secret-key";
        }
    };

    public static final StringGuacamoleProperty DEFAULT_PROTOCOL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "default-protocol";
        }
    };

    public static final IntegerGuacamoleProperty TIMESTAMP_AGE_LIMIT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "timestamp-age-limit";
        }
    };

    public static final StringGuacamoleProperty DC_SERVER_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "dc-server-id";
        }
    };

    public static final StringGuacamoleProperty SERVER_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "server-id";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_ENUMERATE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-enumerate";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_RESTORE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-restore";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_BACKUP = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-backup";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_USERNAME = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-username";
        }
    };

    public static final StringGuacamoleProperty PORT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "port";
        }
    };

    public static final StringGuacamoleProperty REGION = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "region";
        }
    };

    public static final StringGuacamoleProperty MESSENGER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "messenger-server";
        }
    };

    public static final StringGuacamoleProperty PREVENT_CAPS_LOCK = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "prevent-caps-lock";
        }
    };

    public static final StringGuacamoleProperty VM_BACKUP_AWS = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.aws.vm-backup";
        }
    };

    public static final StringGuacamoleProperty VM_RESTORE_AWS = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.aws.vm-restore";
        }
    };

    public static final StringGuacamoleProperty VM_DATETIME_AWS = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.aws.vm-datetime";
        }
    };

    public static final StringGuacamoleProperty VM_BACKUP_AZURE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.azure.vm-backup";
        }
    };

    public static final StringGuacamoleProperty VM_RESTORE_AZURE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.azure.vm-restore";
        }
    };

    public static final StringGuacamoleProperty VM_DATETIME_AZURE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.azure.vm-datetime";
        }
    };

    public static final StringGuacamoleProperty SERVER_GROUP_COUNT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "server-group-count";
        }
    };

    public static final StringGuacamoleProperty HAP_CAPACITY_API_SERVICE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "hap-capacity-api-service";
        }
    };

    public static final StringGuacamoleProperty HAP_CAPACITY_API_SERVICE_REBOOT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "hap-capacity-api-service-reboot";
        }
    };

    public static final StringGuacamoleProperty REDIS_URL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "redis-url";
        }
    };

    public static final IntegerGuacamoleProperty REDIS_THUMB_DB = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "redis-thumb-db";
        }
    };

    /**
     * Check wether to default the One-Time-URLs feature on/off if there is not
     * a ENABLE_ONETIME_URL_PARAM parameter specified in the encrypted payload.
     */
    public static final BooleanGuacamoleProperty DEFAULT_OTU_CHECK = new BooleanGuacamoleProperty() {
        @Override
        public String getName() {
            return "default-otu-check";
        }
    };

    public static final StringGuacamoleProperty API_BASE_DOMAIN = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "api-base-domain";
        }
    };

    public static final StringGuacamoleProperty POWERSHELL_RUNNER_HOST = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "powershell-runner-host";
        }
    };

    public static final BooleanGuacamoleProperty ALLOW_STICKY_CLEARING_API = new BooleanGuacamoleProperty() {
        @Override
        public String getName() {
            return "allow-sticky-clearing-api";
        }
    };

    public static final StringGuacamoleProperty AZURE_VM_SCRIPTS_HOST = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.azure.vm-scripts-host";
        }
    };

    public static final StringGuacamoleProperty AZURE_SUB_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "vm-cloud.azure.azure-sub-id";
        }
    };

    public static final IntegerGuacamoleProperty LATENCY_THRESHOLD = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "latency.threshold";
        }
    };

    public static final IntegerGuacamoleProperty LATENCY_CHECK_INTERVAL = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "latency.check-interval";
        }
    };

    public static final IntegerGuacamoleProperty LATENCY_CHECK_COUNT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "latency.check-count";
        }
    };

    // The secret key for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-secret-key";
        }
    };

    // The secret key for the session management API
    public static final StringGuacamoleProperty SESSION_MGMT_API_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "session-mgmt-api-secret-key";
        }
    };

    // The secret key for the HAProxy
    public static final StringGuacamoleProperty HAP_CAPACITY_API_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "hap-capacity-api-secret-key";
        }
    };

    // The max timeout for checking the server capacity (in milliseconds)
    public static final IntegerGuacamoleProperty MAX_HAP_CAPACITY_TIMEOUT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "max-hap-capacity-timeout";
        }
    };

    // The threshold for checking the server capacity
    public static final IntegerGuacamoleProperty MAX_ALLOWED_FAILED_START = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "max-allowed-failed-start";
        }
    };

    // The h264-max-resolution
    public static final IntegerGuacamoleProperty H264_MAX_RESOLUTION_PROPERTY = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "h264-max-resolution";
        }
    };

    // The port for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_PORT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-port";
        }
    };

    // The port for the session management API
    public static final IntegerGuacamoleProperty SESSION_MGMT_API_PORT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "session-mgmt-api-port";
        }
    };

    // The delay for executing an event of the RDS session logout (in seconds)
    public static final IntegerGuacamoleProperty RDS_LOGOUT_EVENT_DELAY = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "rds-logout-event-delay";
        }
    };

    // API key for accessing file browser API
    public static final StringGuacamoleProperty FILEBROWSER_SERVER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "filebrowser-server";
        }
    };

    // url for accessing chatbot
    public static final StringGuacamoleProperty CHATBOT_SERVER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "chatbot-server";
        }
    };

    // The address of RDP Router Service
    public static final StringGuacamoleProperty RDP_ROUTER_API_SERVICE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-service";
        }
    };

    // The secret key for RDP Router Service
    public static final StringGuacamoleProperty RDP_ROUTER_API_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-secret-key";
        }
    };

    // Delay between retries
    public static final IntegerGuacamoleProperty RDP_ROUTER_API_TIME_OUT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-timeout";
        }
    };

    // The number of calls available to establish the session with RDP Router Service
    public static final IntegerGuacamoleProperty RDP_ROUTER_API_ATTEMPT_COUNT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-attempt-count";
        }
    };

    // Interval to keep the alive session with RDP Router Service
    public static final IntegerGuacamoleProperty RDP_ROUTER_KEEP_ALIVE_INTERVAL = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-keep-alive-interval";
        }
    };

    // API key for accessing file browser API
    public static final StringGuacamoleProperty FILEBROWSER_API_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "filebrowser-api-key";
        }
    };

    // The token for accessing the rollbar
    public static final StringGuacamoleProperty ROLLBAR_ACCESS_TOKEN = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "rollbar-access-token";
        }
    };

    // The gRPC server endpoint for the vusb streaming
    public static final StringGuacamoleProperty GRPC_SERVER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "grpc-server";
        }
    };

    // The SSO certificate generation server endpoint
    public static final StringGuacamoleProperty SSO_CERT_GEN_SERVER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "sso-cert-gen-server";
        }
    };

    // The SSO certificate generation server api key
    public static final StringGuacamoleProperty SSO_CERT_GEN_API_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "sso-cert-gen-api-key";
        }
    };

    // The url for the OTT authentication
    public static final StringGuacamoleProperty OTT_URL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "ott-url";
        }
    };
    
    // The timeout for the OTT authentication in seconds
    public static final IntegerGuacamoleProperty OTT_VALIDATION_TIMEOUT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "ott-validation-timeout";
        }
    };

    private ApportoProperties() {
    }

    public static final Object getProp(String name) {
        switch (name) {
            case "secret-key":
                return SECRET_KEY;
            case "default-protocol":
                return DEFAULT_PROTOCOL;
            case "timestamp-age-limit":
                return TIMESTAMP_AGE_LIMIT;
            case "dc-server-id":
                return DC_SERVER_ID;
            case "server-id":
                return SERVER_ID;
            case "snapshot-enumerate":
                return SNAPSHOT_ENUMERATE;
            case "snapshot-restore":
                return SNAPSHOT_RESTORE;
            case "snapshot-backup":
                return SNAPSHOT_BACKUP;
            case "snapshot-username":
                return SNAPSHOT_USERNAME;
            case "port":
                return PORT;
            case "region":
                return REGION;
            case "messenger-server":
                return MESSENGER;
            case "prevent-caps-lock":
                return PREVENT_CAPS_LOCK;
            case "vm-cloud.aws.vm-backup":
                return VM_BACKUP_AWS;
            case "vm-cloud.aws.vm-restore":
                return VM_RESTORE_AWS;
            case "vm-cloud.aws.vm-datetime":
                return VM_DATETIME_AWS;
            case "vm-cloud.azure.vm-backup":
                return VM_BACKUP_AZURE;
            case "vm-cloud.azure.vm-restore":
                return VM_RESTORE_AZURE;
            case "vm-cloud.azure.vm-datetime":
                return VM_DATETIME_AZURE;
            case "server-group-count":
                return SERVER_GROUP_COUNT;
            case "hap-capacity-api-service":
                return HAP_CAPACITY_API_SERVICE;
            case "hap-capacity-api-service-reboot":
                return HAP_CAPACITY_API_SERVICE_REBOOT;
            case "hap-capacity-api-secret-key":
                return HAP_CAPACITY_API_SECRET_KEY;
            case "max-hap-capacity-timeout":
                return MAX_HAP_CAPACITY_TIMEOUT;
            case "max-allowed-failed-start":
                return MAX_ALLOWED_FAILED_START;
            case "redis-url":
                return REDIS_URL;
            case "redis-thumb-db":
                return REDIS_THUMB_DB;
            case "default-otu-check":
                return DEFAULT_OTU_CHECK;
            case "api-base-domain":
                return API_BASE_DOMAIN;
            case "powershell-runner-host":
                return POWERSHELL_RUNNER_HOST;
            case "allow-sticky-clearing-api":
                return ALLOW_STICKY_CLEARING_API;
            case "vm-cloud.azure.vm-scripts-host":
                return AZURE_VM_SCRIPTS_HOST;
            case "vm-cloud.azure.azure-sub-id":
                return AZURE_SUB_ID;
            case "latency.threshold":
                return LATENCY_THRESHOLD;
            case "latency.check-interval":
                return LATENCY_CHECK_INTERVAL;
            case "latency.check-count":
                return LATENCY_CHECK_COUNT;
            case "apporto-service-secret-key":
                return APPORTO_SERVICE_SECRET_KEY;
            case "session-mgmt-api-secret-key":
                return SESSION_MGMT_API_SECRET_KEY;
            case "h264-max-resolution":
                return H264_MAX_RESOLUTION_PROPERTY;
            case "apporto-service-port":
                return APPORTO_SERVICE_PORT;
            case "session-mgmt-api-port":
                return SESSION_MGMT_API_PORT;
            case "rds-logout-event-delay":
                return RDS_LOGOUT_EVENT_DELAY;
            case "rollbar-access-token":
                return ROLLBAR_ACCESS_TOKEN;
            case "rdp-router-api-service":
                return RDP_ROUTER_API_SERVICE;
            case "rdp-router-api-secret-key":
                return RDP_ROUTER_API_SECRET_KEY;
            case "rdp-router-api-timeout":
                return RDP_ROUTER_API_TIME_OUT;
            case "rdp-router-api-attempt-count":
                return RDP_ROUTER_API_ATTEMPT_COUNT;
            case "rdp-router-keep-alive-interval":
                return RDP_ROUTER_KEEP_ALIVE_INTERVAL;
            case "filebrowser-server":
                return FILEBROWSER_SERVER;
            case "filebrowser-api-key":
                return FILEBROWSER_API_KEY;
            case "chatbot-server":
                return CHATBOT_SERVER;
            case "grpc-server":
                return GRPC_SERVER;
            case "sso-cert-gen-server":
                return SSO_CERT_GEN_SERVER;
            case "sso-cert-gen-api-key":
                return SSO_CERT_GEN_API_KEY;
            case "ott-url":
                return OTT_URL;
            case "ott-validation-timeout":
                return OTT_VALIDATION_TIMEOUT;
            default:
                return null;
        }
    }
}
