/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon', ['angular-intro', 'angular-web-notification', 'ngCookies']).controller('ribbonController',
    ['$scope', '$routeParams', '$timeout', '$injector', 'ServiceParticipant', 'ServiceRoom',
     'ServiceCall', 'webNotification',

        function ribbonController($scope, $routeParams, $timeout, $injector, ServiceParticipant,
                                  ServiceRoom, ServiceCall, webNotification) {

            // Required services
            var guacNotification         = $injector.get('guacNotification');
            var guacClientManager        = $injector.get('guacClientManager');
            var $rootScope               = $injector.get('$rootScope');
            var clipboardService         = $injector.get('clipboardService');
            var authenticationService    = $injector.get('authenticationService');
            var $http                    = $injector.get('$http');
            var $location                = $injector.get('$location');
            var $translate               = $injector.get('$translate');
            var mmonitor                 = $injector.get('mmonitorService');
            var $interval                = $injector.get('$interval');
            var remoteNotifications      = $injector.get('remoteNotificationService');
            var thumbnailService         = $injector.get('thumbnailService');
            var $q                       = $injector.get('$q');
            var $document                = $injector.get('$document');
            var $window                  = $injector.get('$window');
            var ngIntroService           = $injector.get('ngIntroService');
            var ServiceRoom              = $injector.get('ServiceRoom');
            var ServiceParticipant       = $injector.get('ServiceParticipant');
            var ManagedClientState       = $injector.get('ManagedClientState');
            var ClientIdentifier         = $injector.get('ClientIdentifier');
            var connectionService        = $injector.get('connectionService');
            var wwInterval               = $injector.get('wwInterval');
            var userInfoService          = $injector.get('userInfoService');
            var utils                    = $injector.get('utilityService');

            // This service is required by directive used in ribbon template html
            $scope.infoService           = $injector.get('infoService');
            $scope.ribbonService         = $injector.get('ribbonService');
            $rootScope.ribbonService     = $injector.get('ribbonService');
            $scope.circleLoaderService   = $injector.get('circleLoaderService');
            $scope.ServiceCall           = $injector.get('ServiceCall');
            $scope.fileBrowserService    = $injector.get('fileBrowserService');
            $scope.chatbotService        = $injector.get('chatbotService');
            var userInfoService          = $injector.get('userInfoService');

            /**
             * Ribbon should be visible only when remote desktop is visible.
             *
             * @type Boolean
             */
            $rootScope.ribbonVisible    = true;

            $scope.retryCountServerName = 0;

            $scope.dragOptions = {
                container: 'viewport',
                handle: '.chat-head.controlbox-head',
            }
            $scope.dragOptionsCall = {
                container: 'viewport',
                handle: '#call-confirm-dialog',
            }
            $scope.dragOptionsPresenter = {
                container: 'viewport',
                handle: '.presenter-title-dragable',
            }

            $scope.dragOptionsUsbDlg = {
                container: 'viewport',
                handle: '#move-usb-dlg',
            }
            $scope.dragOptionsFileBrowserDlg = {
                container: 'viewport',
                handle: '#move-file-browser-dlg',
            }
            
            $scope.dragOptionsChatbotDlg = {
                container: 'viewport',
                handle: '#move-chat-bot-dlg',
            }

            $scope.hideRibbonBar = false;
            $scope.isSmallRibbonBarVisible = false;
            $scope.timeOutRef = null;
            $scope.previousY;

            // current client instance
            $scope.client = null;
            $scope.managedClients = null;

            $scope.ribbonService.ribbonActive = false;

            $scope.ribbonService.isSharingScreenWith = new URLSearchParams(window.location.href).get('isSharingScreenWith');

            $scope.oldOnUnload = null;

            $scope.authRequests = [];
            $scope.authTokens = [];
            $rootScope.selectedGroup;
            $rootScope.srv1;
            $rootScope.srv2;
            $rootScope.srv3;
            $rootScope.srv4;
            $rootScope.id;

            $rootScope.openedGroups = [];
            $rootScope.closedGroups = [];

            $rootScope.visibleClassroomView = false;

            $rootScope.selectedAssignment;

            // Array of handles to opened virtual classroom windows
            $scope.vcWin       = [];

            var idleWatchVCWin = null;
            var SECOND_UNIT    = 1 * 1000; // 1 s
            var CHECK_VC_WINDOW = 5 * 1000;

            $scope.mmonitorEventSource = null;

            $rootScope.selectedUSB;


            // Ensure that the server name is caught.
            $rootScope.hasServerName = false;

            var introIndex = 1 // index of current intro
            var introTotal = 0 // count of total intro features

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            /**
             * All client error codes handled and passed off for translation. Any error
             * code not present in this list will be represented by the "DEFAULT"
             * translation.
             */
            var CLIENT_ERRORS = {
                0x0201: true,
                0x0202: true,
                0x0203: true,
                0x0207: true,
                0x0208: true,
                0x0209: true,
                0x020A: true,
                0x020B: true,
                0x0301: true,
                0x0303: true,
                0x0308: true,
                0x031D: true,
                0x0320: true
            };

            /**
             * All tunnel error codes handled and passed off for translation. Any error
             * code not present in this list will be represented by the "DEFAULT"
             * translation.
             */
            var TUNNEL_ERRORS = {
                0x0201: true,
                0x0202: true,
                0x0203: true,
                0x0204: true,
                0x0205: true,
                0x0207: true,
                0x0208: true,
                0x0301: true,
                0x0303: true,
                0x0308: true,
                0x031D: true
            };

            var CONNECTION_SUCCESS_TEXT = "SSE Init";
            var CONNECTION_END_TEXT = "SSE End";

            document.addEventListener('mousemove', function(event) {

                // Get the cursor's Y position
                var cursorY = event.clientY;

                // Check if the cursor is near the top of the window
                if (cursorY <= 15) {  // Adjust the threshold value as needed
                    $scope.isSmallRibbonBarVisible = true;
                    $scope.previousY = event.clientY;
                    clearTimeout($scope.timeOutRef);
                }

                  // Check if the cursor is moving from the top to a lower position
                  if ($scope.previousY !== undefined && $scope.previousY <= 15 && cursorY > 16) {
                      $scope.hideSmallRibbonBar();
                  }
            });

            $scope.hideSmallRibbonBar = function hideSmallRibbonBar(){
                clearTimeout($scope.timeOutRef);
                $scope.timeOutRef = setTimeout(()=>{
                    $scope.isSmallRibbonBarVisible = false;
                    $scope.previousY = 50;
                },1000);
            }

            /**
             * Frequency value to check sftp available
             *
             * @type Number
             */
            const CHECKING_FREQUENCY = 10 * 1000; // 10 seconds

            // Adjusting different display for desktop and mobile devices
            var ua = navigator.userAgent;
            var isSmartphone = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua);
            var isTablet = /iPad|Android|PlayBook|Silk|Kindle|Tablet|Tab|GT-P|SM-T|SCH-I|SHW-M/i.test(ua);

            // Check iOS 13
            if (!isTablet && navigator.userAgent.match(/Mac/) && navigator.maxTouchPoints && navigator.maxTouchPoints > 2) {
                isTablet = true;
            }

            if (isTablet || isSmartphone) {
                $timeout(function () {
                    if (window.innerHeight > window.innerWidth) {
                        document.getElementsByClassName('ribbon-bar')[0].style.overflowX = 'auto';
                    }

                    document.getElementsByClassName('ribbon-bar')[0].style.display = 'flex';

                }, 5000);

                $scope.desktop = false;
                $scope.tablet = true;
            }
            else {
                $scope.desktop = true;
                $scope.tablet = false;
            }

            if ((/iPad|iPhone|iPod/.test(navigator.userAgent)) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 2)) {
                    $scope.ribbonService.licenses.hasFullscreenLicence = false;
            }
            else {
                    $scope.ribbonService.licenses.hasFullscreenLicence = true;
            }

            if ($scope.tablet)
                $scope.ribbonService.licenses.hasClassroomLicence = false;
                $scope.ribbonService.licenses.hasPresenterLicence = false;

            if ($scope.ribbonService.browser.isChrome || $scope.ribbonService.browser.isFirefox) {
                window.screen.orientation.addEventListener('change', function () {
                    window.location.reload();
                });
            }
            else if ($scope.ribbonService.browser.isSafari) {
                window.onorientationchange = function () {
                    window.location.reload();
                }
            }

            if (webNotification || !$scope.tablet) {
                webNotification.requestPermission(function onRequest(granted) {
                    if (granted) {
                        console.log('Notification permission is granted.');
                    }
                    else {
                        console.log('Notification permission is not granted.');
                    }
                });
            }

            /**
             * @type NoSleep
             */
            var noSleep = null;

            // Create NoSleep object and Enable NoSleep mode
            function enableNoSleep() {
                if (!noSleep) noSleep = new NoSleep();
                document.addEventListener('click', function enableNoSleep() {
                    if (noSleep) noSleep.enable();
                    document.removeEventListener('click', enableNoSleep, false);
                }, false);
            }

            // Disable NoSleep mode and delete object
            function disableNoSleep() {
                if (noSleep) noSleep.disable();
                noSleep = null;
            }

            /**
             * Check the idle time
             */
            var isFirstIdleCheck       = true;
            var idleTimeChecker        = null;
            var idleTime               = 0;

            var firstIdleTimerWarning;   // Maximum idle time (mins) for 15 mins before
            var secondIdleTimerWarning;  // Maximum idle time (mins) for  5 mins before

            var FIRST_IDLE_EXPIRED_WARNING_TIME  = 15; // Waiting Time 15mins before idle is expired.
            var SECOND_IDLE_EXPIRED_WARNING_TIME =  5; // Waiting Time  5mins before idle is expired.

            // Timeout value to use on multi monitor.
            var idleTimeout = "0";

            // Timeout value of showing the introduction popup (6 seconds)
            let INTRO_POPUP_TIMEOUT = 6 * 1000;

            // function to reset the idleTime.
            function resetIdleTime() {
                if (!$scope.ribbonService.isIdleExpired && idleTimeChecker)
                    idleTime = 0;
            }

            // Zero the idle timer on mouse movement and key press.
            $window.onmousemove = function () {
                resetIdleTime();
            };

            $window.onmousedown = function () {
                resetIdleTime();
                var ribbonBar = document.querySelector("ribbon-bar");
                if (ribbonBar && $rootScope.isRemoteDesktop !== undefined && !$rootScope.isRemoteDesktop) {
                    $rootScope.isRemoteDesktop = true;
                    if ($scope.targetElement) {
                        $scope.targetElement.classList.remove("ribbon-button-active");
                        $scope.targetElement.blur();
                    }
                }
            };

            $window.onkeydown = function () {
                resetIdleTime();
            };

            // Count the idle time.
            function checkIdleTime() {
                idleTime = idleTime + 1;

                if (idleTime == firstIdleTimerWarning) {
                    showIdleExpiredWarning(FIRST_IDLE_EXPIRED_WARNING_TIME);
                }
                else if (idleTime == secondIdleTimerWarning) {
                    showIdleExpiredWarning(SECOND_IDLE_EXPIRED_WARNING_TIME);
                }
                else if (idleTime == parseInt(idleTimeout)) {
                    guacNotification.showStatus(false);
                    // Show the session expired dialog
                    guacNotification.showStatus({
                        title   : "DIALOGS.SESSION_EXPIRED_TITLE",
                        text    : {
                            key : "DIALOGS.SESSION_EXPIRED_BODY"
                        }
                    });
                    resetIdleTime();
                    wwInterval.wwSendMessage({ type: "IDLE_TIME", cancelTimer: true });
                    wwInterval.wwTerminate();
                    idleTimeChecker = null;
                    $rootScope.ribbonService.isIdleExpired = true;

                    $scope.ribbonService.userinfo.state = '3';
                    $scope.ribbonService.roomState = {};
                    var kurentos = ServiceRoom.getKurentos();
                    if (kurentos) {
                        for (var roomName in kurentos) {
                            if (!!kurentos[roomName] && !!kurentos[roomName].close) {
                                kurentos[roomName].close();
                                ServiceRoom.setKurento(null, roomName);
                            }
                        }
                    }
                }
            }

            /**
             * Show the browser notification with the time before the idle is expired.
             *
             * @param {Integer} idleExpiredWarningTime
             *      Time before the idle is expired.
             */
            var showIdleExpiredWarning = function showIdleExpiredWarning (idleExpiredWarningTime) {
                $translate('DIALOGS.WARNING_INACTIVITY_TITLE').then(function setTitle(title) {
                    $translate('DIALOGS.WARNING_INACTIVITY_MESSAGE_NOTIFICATION').then(function setTitle(body) {
                        webNotification && webNotification.showNotification(title, {
                            body: body.replace("(mins)", idleExpiredWarningTime),
                            requireInteraction: true,
                            onClick: function onNotificationClicked() {
                                try {
                                    window.focus();
                                    this.cancel();
                                }
                                catch (ex) {
                                }
                            },
                            icon: 'app/ext/ribbon/images/favicons/apple-icon-60x60.png'
                        }, function onShow(error, hide) {
                            if (error) {
                                console.log('Unable to show notification: ' + error.message);
                            }
                        });
                    });
                });
            }

            // check filebrowser status configured or not
            $scope.ribbonService.getChatbotUrl = function getChatbotUrl() {
                const clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                const datasource = encodeURIComponent(clientIdentifier.dataSource);
                
                const httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };

                var deferred = $q.defer();
                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + datasource + "/chatbot/url/",
                    params: httpParameters
                };

                $http(req)
                .then(function(response) {
                    if (isValidUrl(response.data.url)) {
                        $scope.ribbonService.chatbotUrl = response.data.url;
                        deferred.resolve(response.data.url);
                    } else {
                        deferred.reject("Invalid chatbot URL: " + response.data.url);
                    }
                })
                .catch(function(response) {
                    console.error("Error retrieving chatbot url: ", response.message);
                    deferred.reject(response.message);
                });

                return deferred.promise;
            }

            // check filebrowser status configured or not
            $scope.ribbonService.checkFileBrowserStatus = function checkFileBrowserStatus() {
                const clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                const datasource = encodeURIComponent(clientIdentifier.dataSource);
                const httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };
                const req = {
                    method: 'GET',
                    url: "api/session/ext/" + datasource + "/filebrowser/status/",
                    params: httpParameters
                };

                $http(req)
                .then(function(response) {
                    console.log(response,'response')
                    if (response.data==='status-configured') {
                        $scope.ribbonService.fileBrowserReady = true;
                    }
                    else {
                        console.log("FileBrowser button is disabled beacuse server don't have guacamole.properties - filebrowser-server and filebrowser-api-key properties");
                        $.scope.ribbonService.fileBrowserReady = false;
                    }
                })
                .catch(function(response) {
                    console.error("Error retrieving file browser url: ", response.data.message);
                })
                .finally( function() {
                    // Empty process
                });
            }

            // Get Server Name
            function getServerName() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                };
                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + authenticationService.getDataSource() + "/servername",
                    params: httpParameters
                };

                $http(req)
                .then(function (response) {
                    var data = response.data;
                    if (!data.servername && $scope.retryCountServerName < $scope.ribbonService.MAX_RETRY_COUNT) {
                        setTimeout(function () {
                            $scope.retryCountServerName++;
                            getServerName();
                        }, 5000);
                    }
                    else {
                        var servername = '';
                        if ($scope.retryCountServerName < $scope.ribbonService.MAX_RETRY_COUNT) {
                            servername = data.servername;
                        }
                        else {
                            servername = data.hostname;
                        }

                        $rootScope.servername = servername;
                        $rootScope.grpc_server = data.grpc_server;
                        console.debug("getServerName: " + servername + " retryCount: " + $scope.retryCountServerName);
                        $rootScope.hasServerName = true;

                        $scope.client.client.requestCameraStream($scope.client.client, servername, $scope.ribbonService.browser);
                    }
                }).catch(function (response) {
                    if (response) {
                        console.error("There is an error in getting the server name: ", response.message);
                    }
                    else {
                        console.error("There is an error in getting the server name");
                    }
                }).finally(function () { })
            }

            /* FullScreen is the first focus */
            $scope.$watch('ribbonService.licenses.hasFullscreenLicence', function() {
                $rootScope.firstFocusableElement = null;
                $rootScope.lastFocusableElement = null;

                setTimeout(function () {
                    if (document.querySelector('.compressed_ribbon_bar_btn')) {
                        document.querySelector('.compressed_ribbon_bar_btn').focus();
                    }
                }, 101);
            });

            $scope.$watch('client.clientState.connectionState', function clientStateChanged(connectionState) {
                if (connectionState === ManagedClientState.ConnectionState.CONNECTED && $scope.ribbonService.licenses.hasClipboard) {

                    // Sync with local clipboard
                    var cliprdr = clipboardService.getLocalClipboard();
                    if (cliprdr === null) {
                        // Hide status notification
                        guacNotification.showStatus(false);
                        return;
                    }

                    cliprdr.then(function clipboardRead(data) {
                        $scope.$broadcast('guacClipboard', data);
                    }, angular.noop);

                    // Hide status notification
                    guacNotification.showStatus(false);

                }

                // When the 3rd monitor is opened, show the notification.
                if (connectionState === ManagedClientState.ConnectionState.CONNECTED) {
                    if ($scope.ribbonService.isShared) {
                        if ($routeParams.hasOwnProperty("mm") && $routeParams.mm === "2") {
                            $translate('MULTI_MONITOR.INFO_TEXT').then(function (text) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = text;
                                $scope.infoService.infoDialogVisible = true;
                            });

                            $timeout(function hideInfo() {
                                $scope.infoService.infoDialogVisible = false;
                            }, $scope.ribbonService.thresholdInfo);
                        }
                    }
                }
            });

            $scope.$watch('ribbonService.networkQualityDialogVisible', function (visible) {
                var networkIndicatorBtn = document.getElementById("network-indicator-btn");
                if (visible) {
                    networkIndicatorBtn.setAttribute("aria-expanded", "true");
                    // Google Tag Manager
                    dataLayer.push({
                        event: 'Network Indicator',
                        button_name: 'btn_ribbon_network_indicator',
                        sub_domain: $scope.ribbonService.licenses.subdomain
                    });

                    $rootScope.setFocusableElements("#dlg-network-quality");
                }
                else {
                    networkIndicatorBtn.setAttribute("aria-expanded", "false");
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.ribbon-network-indicator')) {
                            document.querySelector('.ribbon-network-indicator').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watchGroup(['ribbonService.ribbonShareDialogVisible', 'ribbonService.isSharingStart', 'ribbonService.shareableStarted'], function (visible) {
                if (visible[0] && visible[2]) {
                    $rootScope.setFocusableElements(".ribbon-share-dialog", "now");
                } else if (visible[0]) {
                    $rootScope.setFocusableElements(".ribbon-share-dialog");
                } else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-share')) {
                            document.querySelector('.btn-share').focus();
                        }
                    }, 100);
                }
            });

            $scope.assignmentTitle = 'assignments';

            $scope.$watchGroup([ "ribbonService.licenses.hasLTILicence", "ribbonService.fileExplorerReady" ], function (newValues) {
                const [hasLTI, fileReady] = newValues;

                if (!hasLTI) {
                  $scope.assignmentTitle = $translate.instant("RIBBON.PUBLISH_NOT_LICENSED");
                } else if (!fileReady) {
                  $scope.assignmentTitle = $translate.instant("RIBBON.CONNECTING");
                } else {
                  $scope.assignmentTitle = $translate.instant("RIBBON.ASSIGNMENT_BTN_TITLE",{courseName:$scope.ribbonService.licenses.ltiCourseName});
                }
            });


            $scope.$watch('ribbonService.summaryChartVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("summary-chart-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-analytics')) {
                            document.querySelector('.btn-analytics').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.chattingMinimized', function (visible) {
                if (!visible) {

                    $rootScope.setFocusableElements("#chatting-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-chat')) {
                            document.querySelector('.btn-chat').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.presenterRequestDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("presenter-request-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-presenter')) {
                            document.querySelector('.btn-presenter').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.messengerShareDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("messenger-share-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.message-input')) {
                            document.querySelector('.message-input').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ServiceCall.callDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("#call-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.message-input')) {
                            document.querySelector('.message-input').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('infoService.infoDialogVisible', function (visible) {
                if($scope.ribbonService.snapshotManagerVisible ||
                    $scope.ribbonService.ribbonShareDialogVisible
                ) // adding condition to avoid focus on info dialog
                    return;

                if (visible) {
                    setTimeout(function() {
                        var modal = document.querySelector("info-message");

                        if (modal) {
                            var focusableContent = modal.querySelectorAll($scope.ribbonService.FOCUSABLE_ELEMENTS);

                            $rootScope.firstInfoFocusableElement = focusableContent[0];
                            $rootScope.lastInfoFocusableElement = focusableContent[focusableContent.length - 1];

                            $rootScope.firstInfoFocusableElement.focus();
                        }
                    }, 0);
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;
                }
            });

            $scope.$watch('ribbonService.snapshotManagerVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("#snapshot-manager-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-snapshot-manager')) {
                            document.querySelector('.btn-snapshot-manager').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.classroomDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("classroom-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-classroom-mandatory')) {
                            document.querySelector('.btn-classroom-mandatory').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.presenterDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("presenter-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-presenter')) {
                            document.querySelector('.btn-presenter').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.assignmentManagementDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("assignment-management-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-assignment-manage')) {
                            document.querySelector('.btn-assignment-manage').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.presenterThumbnailVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("presenter-thumbnail");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-presenter')) {
                            document.querySelector('.btn-presenter').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.vmManagerVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements("vm-manager-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-backup')) {
                            document.querySelector('.btn-backup').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.fileExplorerVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements(".file-explorer");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-file-download ')) {
                            document.querySelector('.btn-file-download ').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.innerFileBrowserVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements(".file-browser-external");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-file-browser')) {
                            document.querySelector('.btn-file-browser').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.enableLoggingDialogVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements(".logging-dialog");
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('#ribbonHelpCenterBtn')) {
                            document.querySelector('#ribbonHelpCenterBtn').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('ribbonService.usbListDlgVisible', function (visible) {
                if (visible) {
                    $rootScope.setFocusableElements(".usb-list-dialog");
                } else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function() {
                        if (document.querySelector('#ribbonSettingBtn')) {
                            document.querySelector('#ribbonSettingBtn').focus();
                        }
                    }, 100);
                }
            });

            $scope.$watch('hideRibbonBar', function (hidden) {
                
                var compressedRibbonBarButton = document.getElementById("compressed_ribbon_bar_btn");
                if (hidden) compressedRibbonBarButton.setAttribute('aria-expanded', 'false');
                else compressedRibbonBarButton.setAttribute('aria-expanded', 'true');
            });

            function setShareDialogFocusableElements(focusableContent, param = null) {

                var focusableContentArray = Array.from(focusableContent);
                var collaborationNowElements = Array.from(document.querySelectorAll('.collaboration-now *'));
                var collaborationOfflineElements = Array.from(document.querySelectorAll('.collaboration-offline *'));
                var collaborationSelectElements = Array.from(document.querySelectorAll('.collaboration-select *'));

                var filteredArray = [];

                if (!$scope.ribbonService.nextButtonClicked) {

                    for (var i = 0; i < focusableContentArray.length; i++) {
                        var el = focusableContentArray[i];
                        if (!collaborationNowElements.includes(el) && !collaborationOfflineElements.includes(el)) {
                            filteredArray.push(el);
                        }
                    }
                } else {
                    if ($scope.ribbonService.nextButtonClicked && param === 'now') {

                        for (var i = 0; i < focusableContentArray.length; i++) {
                            var el = focusableContentArray[i];
                            if (!collaborationSelectElements.includes(el) && !collaborationOfflineElements.includes(el)) {
                                filteredArray.push(el);
                            }
                        }
                    } else if ($scope.ribbonService.nextButtonClicked && param === 'offline') {
                        for (var i = 0; i < focusableContentArray.length; i++) {
                            var el = focusableContentArray[i];
                            if (!collaborationSelectElements.includes(el) && !collaborationNowElements.includes(el)) {
                                filteredArray.push(el);
                            }
                        }
                    }
                }
                return filteredArray;
            }

            $rootScope.setFocusableElements = function setFocusableElements(selector, param = null) {
                setTimeout(function() {
                    var modal = document.querySelector(selector);
                    var focusableContent = modal.querySelectorAll($scope.ribbonService.FOCUSABLE_ELEMENTS);

                    if ($scope.ribbonService.ribbonShareDialogVisible) {
                        focusableContent = setShareDialogFocusableElements(focusableContent, param);
                    }

                    if (focusableContent.length > 0) {
                        $rootScope.firstFocusableElement = focusableContent[0];
                        $rootScope.lastFocusableElement = focusableContent[focusableContent.length - 1];

                        setTimeout(function() {
                            $rootScope.firstFocusableElement.focus();
                        }, 50);
                    }
                }, 50);
            };

            /**
             * Watch if the user is logged on
             */
            $rootScope.$watch('session_id', function sessionIdChanged(session_id) {

                // If the introduction flag is enabled, show the popup
                // after a user is logged on.
                if ($routeParams.intro === "1" && session_id > 0) {
                    $timeout(function() {
                        showIntroPopup();
                    }, INTRO_POPUP_TIMEOUT);
                }

                // If the camera feature is available, please try to get the RDS name.
                if (session_id && ($scope.ribbonService.licenses.hasCameraLicence
                    || $scope.ribbonService.licenses.hasUSBLicence)) {
                    $scope.retryCountServerName = 0;
                    getServerName();
                }

            });

            /**
             * Display analytics dashboard
             */
            $scope.displayAnalyticDashboard = function displayAnalyticDashboard() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Analytics',
                    button_name: 'btn_ribbon_analytics',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.summaryChartVisible = true;
            };

            /**
             * Add tooltip to the disabled buttons
             * Angular always removes tooltips for disabled buttons.
             */
            $scope.$watch('$scope.ribbonService.licenses.hasUploadLicence', function () {
                if (!$scope.ribbonService.licenses.hasUploadLicence) {
                    $translate('RIBBON.UPLOAD_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-file-upload')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasDownloadLicence', function () {
                if (!$scope.ribbonService.licenses.hasDownloadLicence) {
                    $translate('RIBBON.DOWNLOAD_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-file-download')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasSharingLicence', function () {
                if (!$scope.ribbonService.licenses.hasSharingLicence) {
                    $translate('RIBBON.SHARING_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-share')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasActivityTrackLicence', function () {
                if (!$scope.ribbonService.licenses.hasActivityTrackLicence) {
                    $translate('RIBBON.ANALYTICS_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-share')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasMessengerLicence', function () {
                if (!$scope.ribbonService.licenses.hasMessengerLicence) {
                    $translate('RIBBON.MESSENGER_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-messenger')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasMessengerLicence', function () {
                if (!$scope.ribbonService.licenses.hasMessengerLicence) {
                    $translate('RIBBON.MESSENGER_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-chat')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasSnapshotsLicence', function () {
                if (!$scope.ribbonService.licenses.hasSnapshotsLicence) {
                    $translate('RIBBON.SNAPSHOTS_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-snapshot-manager')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasHighlightLicence', function () {
                if ($scope.ribbonService.highlightAvailable && !$scope.ribbonService.licenses.hasHighlightLicence) {
                    $translate('RIBBON.HIGHLIGHT_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-highlight')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('$scope.ribbonService.licenses.hasMounterLicence', function () {
                if (!$scope.ribbonService.hasMounterLicence) {
                    $translate('RIBBON.CLOUD_NOT_LICENSED').then(function setTitle(text) {
                        var el = document.getElementsByClassName('btn-cloud')[0];
                        if (el) el.title = text;
                    });
                }
            });
            $scope.$watch('ribbonService.licenses.hasClassroomLicence', function () {
                if ($scope.ribbonService.licenses.hasClassroomLicence) {
                    getUsersGroups();
                }
            });
            $scope.$watch('ribbonService.licenses.hasPresenterLicence', function () {
                if ($scope.ribbonService.licenses.hasPresenterLicence) {
                    getUsersGroups();
                }
            });
            $scope.$watch('ribbonService.licenses.launchFullScreen', function () {
                if (!$rootScope.isKioskMode && $scope.ribbonService.licenses.launchFullScreen)
                    $scope.toggleFullScreen();
            });

            $scope.$watch('ribbonService.ribbonActive', function (visible) {
                if ((isTablet || isSmartphone) && visible) {
                    $scope.ribbonService.ribbonActive = false;
                }
            });

            $scope.$watch('ribbonService.userinfo.state',function(userState){
              if(userState != "3" && $scope.ribbonService.chattingVisible){
                $rootScope.setFocusableElements('#chatting-dialog')
              }
            })

            /*
             * This function sleeps in the specified duration in milliseconds
             */
            function sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            /**
             * Fetch groups
             */
            var getUsersGroups = function getUsersGroups() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                };
                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + authenticationService.getDataSource() + "/classroom/getPermissionGroups",
                    params: httpParameters
                };

                $http(req)
                .then(function (response) {
                    var data = response.data;
                    $scope.ribbonService.classroomGroups = data;
                    $scope.ribbonService.presenterGroups = data;

                    // Disable classroom functionality if there are no groups assigned to the user
                    if ($scope.ribbonService.classroomGroups == null || $scope.ribbonService.classroomGroups.length == 0) {
                        $scope.ribbonService.licenses.hasClassroomLicence = false;
                        $scope.ribbonService.licenses.hasPresenterLicence = false;
                    }
                    else {
                        $rootScope.selectedGroup = $scope.ribbonService.classroomGroups[0];
                    }
                }).catch(async function (response) {
                    console.error("Classroom error group fetch: ", response.message);
                    await sleep(CHECKING_FREQUENCY);
                    getUsersGroups();
                }).finally(function () { })
            }

            /**
             * Show or hides share dialog
             */
            $scope.showHideDialog = function showHideDialog() {
                getServerId().then(function () {
                    if ($scope.ribbonService.licenses.hasSharingLicence) {
                        if (!$scope.ribbonService.licenses.hasAsyncCollaborationLicence) {
                            // Skip dialog for selecting collaboration now and collaboration offline
                            $scope.ribbonService.nextButtonClicked = true;
                            $rootScope.$broadcast("skipFirstDialog");
                        }
                    }
                    else {
                        if ($scope.ribbonService.licenses.hasAsyncCollaborationLicence) {
                            // Skip dialog for selecting collaboration now and collaboration offline
                            $scope.ribbonService.nextButtonClicked = true;
                            $rootScope.$broadcast("skipFirstDialog");
                        }
                    }
                    $scope.ribbonService.ribbonShareDialogVisible = !$scope.ribbonService.ribbonShareDialogVisible;
                });
            }

            $scope.$on('$routeChangeSuccess', function (event, current, previous) {

                // If the current route is available
                if (current.$$route) {

                    // Display ribbon if remote client is showing
                    if (current.$$route.bodyClassName === "client") {
                        $rootScope.ribbonVisible = true;
                    }
                    else {
                        $rootScope.ribbonVisible = false;
                    }
                }

                $scope.ribbonService.isShared = $routeParams.hasOwnProperty("key");

                if ($routeParams.id) {
                    $scope.managedClients = guacClientManager.getManagedClients();
                }
            });

            // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
            // try to get licence after 10 seconds.
            $timeout(function () {
                if ($routeParams.id && !$scope.managedClients) {
                    $scope.managedClients = guacClientManager.getManagedClients();
                }
            }, 10 * 1000);

            $scope.$watchCollection('managedClients', function () {
                if ($routeParams.id) {
                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                    if (!$scope.ribbonService.firstStartMessenger && $scope.client != null && $routeParams.id && authenticationService.getDataSource()) {
                        $scope.$watch('ribbonService.licenses.hasMessengerLicence', function () {
                            if ($scope.ribbonService.licenses.hasMessengerLicence) {
                                $scope.ribbonService.toggleMessenger = true;
                                $scope.$broadcast('toggleMessenger');
                            }
                        });
                    }
                }
            });

            /**
             * This reset the idle time when the message is received from the source
             *
             * @param {Event} event
             *      This is the message event that is received when a message is coming from the source.
             */
            $scope.getUserAction = function getUserAction(event) {

                if (event.data) {
                    // Avoid the javascript error due to the text indicating to the connection success ("SSE Init").
                    if (event.data === CONNECTION_SUCCESS_TEXT)
                        return;

                    // Process "SSE End"" message
                    if (event.data === CONNECTION_END_TEXT) {
                        if ($scope.mmonitorEventSource) {
                            $scope.mmonitorEventSource.close();  // Close SSE connection
                            $scope.mmonitorEventSource = null;
                        }

                        $rootScope.$broadcast('multiMonitorNotificationsEnded');

                        console.log("Closed SSE connection for multimonitor.");
                        return;
                    }

                    data = JSON.parse(event.data);
                    if (data) {
                        resetIdleTime();
                    }
                }
            }

            /**
             * Start the thread that receives the signals from other monitors.
             */
            function startMMonitorNotification() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                getClientId(datasource, clientIdentifier)
                .then(function(clientId) {
                    // Create the EventSource for message exchange between monitors.
                    var sessionId = encodeURIComponent(clientId);
                    var token = encodeURIComponent(authenticationService.getCurrentToken());

                    $scope.mmonitorEventSource = new EventSource("api/session/ext/" + datasource + "/mmonitornotify/subscribe" + "?token=" + token + "&sessionId=" + sessionId);
                    $scope.mmonitorEventSource.onmessage = $scope.getUserAction;

                    $rootScope.$broadcast('multiMonitorNotificationsStarted');
                });
            }

            /**
             * End the thread that receives the signals from other monitors.
             */
            function endMMonitorNotification() {

                if ($scope.mmonitorEventSource) {
                    // Unsubscribe the multi monitor notification
                    $scope.mmonitorEventSource.close();
                    $scope.mmonitorEventSource = null;
                }

                // Call the API to end the thead
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var params = "?token=" + authenticationService.getCurrentToken();
                params = params + "&sessionId=" + clientIdentifier.id;

                navigator.sendBeacon("api/session/ext/" + datasource + "/mmonitornotify/end" + params);
            }

            /**
             * Client id is retrieved in a different way for a normal and shared session.
             * This function unifies retrieval of the client id.
             */
            function getClientId(datasource, clientIdentifier) {
                var deffered = $q.defer();

                if (datasource === "encryptedurl-jdbc")
                    deffered.resolve(clientIdentifier.id);
                else {
                    if($location.path().indexOf("classroom") > -1) {
                        return;
                    }

                    connectionService.getConnection(datasource, clientIdentifier.id)
                    .then(function connectionRetrieved(connection) {
                        deffered.resolve(connection.name);
                    });
                }

                return deffered.promise;
            };

            $scope.$on('reconnect:enable-h264', function reconnectH264(event, isEnable264) {
                // End the thread that receives the signals from other monitors.
                if ($scope.mmonitorEventSource != null || !$routeParams.hasOwnProperty("mm")) {
                    endMMonitorNotification();
                }
                $location.search('enable-h264', isEnable264);
            });

            /**
             * Monitor for connection state and apply specific actions dependint on the state.
             */
            $scope.$watch('client.clientState.connectionState', function clientStateChanged(connectionState) {
                // Do not do anything if status not known
                if (!connectionState)
                    return;

                // Prevent device sleep when guacamole client connected
                if (connectionState === ManagedClientState.ConnectionState.CONNECTED) {
                    enableNoSleep();
                }
                else {
                    disableNoSleep();
                }

                // Get any associated status code
                var status = $scope.client.clientState.statusCode;
                if (connectionState === ManagedClientState.ConnectionState.CLIENT_ERROR) {
                    // Determine translation name of error
                    var errorName = (status in CLIENT_ERRORS) ? status.toString(16).toUpperCase() : "DEFAULT";

                    if (errorName === '202') {
                        $translate("CLIENT.ERROR_POOR_NETWORK_NOTIFICATION").then(function setTitle(text) {
                            poorNetworkWarningMessage(text);
                        });
                    }
                }
                // Tunnel error
                else if (connectionState === ManagedClientState.ConnectionState.TUNNEL_ERROR) {
                    // Determine translation name of error
                    var errorName = (status in TUNNEL_ERRORS) ? status.toString(16).toUpperCase() : "DEFAULT";

                    if (errorName === '202' || errorName === 'DEFAULT') {
                        $translate("CLIENT.ERROR_POOR_NETWORK_NOTIFICATION").then(function setTitle(text) {
                            poorNetworkWarningMessage(text);
                        });
                    }
                }

                // This is called whenever connection is established. Checks if the URL multimonitor parameter "mm" is set to 1.
                // If it is set, then this is multimonitor sharing window and its position should be adjusted to show properly secondary screen.
                switch (connectionState) {
                    case ManagedClientState.ConnectionState.CONNECTED:

                        idleTimeout = $scope.ribbonService.licenses.idleTimeout;

                        if ($routeParams.hasOwnProperty("mm")) {
                            idleTimeout = $routeParams.idleTimeout;
                            $scope.ribbonService.resizeMethod = parseInt($routeParams.resizeMethod);
                        }

                        if (isFirstIdleCheck) {
                            if (idleTimeout != '' && idleTimeout != 'false' && idleTimeout != '0') {
                                firstIdleTimerWarning  = parseInt(idleTimeout) - FIRST_IDLE_EXPIRED_WARNING_TIME;
                                secondIdleTimerWarning = parseInt(idleTimeout) - SECOND_IDLE_EXPIRED_WARNING_TIME;

                                wwInterval.wwCreate();
                                wwInterval.wwSendMessage({ type: "IDLE_TIME", startTimer: true });
                                wwInterval.wwSetCallback(function(e) {
                                    if (e.data.type == "IDLE_TIME") {
                                        if (e.data.idleTimeChecker) {
                                            idleTimeChecker = e.data.idleTimeChecker;
                                        }

                                        if (e.data.idleTime) {
                                            checkIdleTime();
                                        }
                                    }
                                });

                                isFirstIdleCheck = false;
                            }
                        }

                        // Start the thread that receives the signals from other monitors.
                        if (!$scope.ribbonService.isPrimaryScreen && $routeParams.hasOwnProperty("mm")) {
                            startMMonitorNotification();
                        }

                        if (!$scope.ribbonService.isIdleExpired)
                            idleTime = 0;

                        if ($scope.client != null &&                                                            // client is initialized
                           ($scope.ribbonService.licenses.hasMMonitorLicence || $routeParams.mm === "1" || $routeParams.mm === "2") &&    // multimonitor mode active and is secondary
                          !($routeParams.hasOwnProperty("key") && !$routeParams.hasOwnProperty("mm"))) {      // it is not regular shared session
                            console.log("initMultiMonitor");
                            mmonitor.initMultiMonitor($scope.client);
                            mmonitor.startMonitorChecker();
                        }

                        if ($rootScope.snapshotRestoreState) {
                            $rootScope.snapshotRestoreState = false;
                            var snapshotRestoreChecker = $interval(function () {
                                if ($rootScope.snapshotId) {
                                    $translate('CLIENT.RECONNECT_SNAPSHOTS_RESTORE', {SNAPSHOT_ID: $rootScope.snapshotId}).then(function setError(text) {
                                        $scope.infoService.top = true;
                                        $scope.infoService.infoText = text;
                                        $scope.infoService.infoDialogVisible = true;
                                    });
                                    $interval.cancel(snapshotRestoreChecker);
                                    snapshotRestoreChecker = null;
                                }
                            }, 3 * 1000);
                        }
                        else if ($rootScope.snapshotSaveState) {
                            $rootScope.snapshotSaveState = false;
                            var snapshotRestoreChecker = $interval(function () {
                                $translate('CLIENT.RECONNECT_SNAPSHOTS_SAVE').then(function setError(text) {
                                    $scope.infoService.top = true;
                                    $scope.infoService.infoText = text;
                                    $scope.infoService.infoDialogVisible = true;
                                });
                                $interval.cancel(snapshotRestoreChecker);
                                snapshotRestoreChecker = null;
                            }, 3 * 1000);
                        }

                        if (!$scope.ribbonService.licenses.hasClipboard) {
                            $scope.client.client.onclipboard = null;
                        }

                        if (!$scope.ribbonService.isShared) {
                            thumbnailService.start();
                            remoteNotifications.start();
                        }

                        if (authenticationService.getDataSource() === 'encryptedurl-jdbc-shared')
                            return;

                        $q.all([
                            getServerId(),
                            getServerGroupCount(),
                            getMessengerHostname(),
                            userInfoService.getUserInfo(),
                        ])
                        .then(function() {
                            $scope.ribbonService.classroomReady = true;

                            if ($scope.ribbonService.licenses.hasMessengerLicence) {
                                var roomName = $scope.ribbonService.GLOBAL_ROOM_NAME;
                                if (!ServiceRoom.getKurento(roomName)) {
                                    $q.all([
                                        getUserList(),
                                    ])
                                    .then(function() {
                                        if ($scope.ribbonService.licenses.hasChattingLicence && $scope.ribbonService.userlist && $scope.ribbonService.userlist.length != 0) {
                                            register();
                                        }
                                    })
                                }
                            }
                        });
                        break;

                    case ManagedClientState.ConnectionState.DISCONNECTED:
                    case ManagedClientState.ConnectionState.CLIENT_ERROR:
                        $scope.ribbonService.userinfo.state = '3';
                        $scope.ribbonService.roomState = {};
                        var kurentos = ServiceRoom.getKurentos();
                        if (kurentos) {
                            for (var roomName in kurentos) {
                                if (!!kurentos[roomName] && !!kurentos[roomName].close) {
                                    kurentos[roomName].close();
                                    ServiceRoom.setKurento(null, roomName);
                                }
                            }
                        }

                        relaseIdleTimeTrackHandler();
                    case ManagedClientState.ConnectionState.TUNNEL_ERROR:
                        $scope.ribbonService.serverId = null;

                        for (var groupName in $scope.ribbonService.groupMembers) {
                            if (!!$scope.ribbonService.groupMembers[groupName]) {
                                $scope.ribbonService.groupMembers[groupName].map(function (member) {
                                    member.state = '3';
                                    return member;
                                });
                            }
                        }

                        if ($scope.ribbonService.licenses.hasMessengerLicence) {
                            $scope.ribbonService.activeMembers = [];
                            $scope.ribbonService.chattingMinimized = true;
                        }

                        if (!$scope.ribbonService.isShared) {
                            remoteNotifications.stop();
                            thumbnailService.stop();
                        }

                        if ($rootScope.onCloseWindowsForPresenter !== undefined) {
                            $rootScope.onCloseWindowsForPresenter();
                        }

                        if ($scope.ribbonService.isPresenter) {
                            $scope.ribbonService.presenterThumbnailVisible = false;
                            $rootScope.isPresenterEnabled = false;
                            $scope.ribbonService.isPresenter = false;
                        }

                        $rootScope.session_id = 0;
                        $rootScope.servername = null;
                        $rootScope.grpc_server = null;

                        // End the thread that receives the signals from other monitors.
                        if ($scope.mmonitorEventSource != null || !$routeParams.hasOwnProperty("mm")) {
                            endMMonitorNotification();
                        }

                        relaseIdleTimeTrackHandler();
                        break;

                    default:
                        break;
                }

            });

            function relaseIdleTimeTrackHandler() {
                if (idleTimeChecker && wwInterval.timeTrackingWorker) {
                    resetIdleTime();
                    wwInterval.wwSendMessage({ type: "IDLE_TIME", cancelTimer: true });
                    idleTimeChecker = null;
                    isFirstIdleCheck = true;
                }

                wwInterval.wwTerminate();
            }

            /**
             * Get auth tokens for all servers behind LB
             */
            function getAuthTokens() {
                var deferred = $q.defer();

                if (utils.countTokens($scope.authRequests) > 0) {
                    return deferred.resolve();
                }

                if ($scope.ribbonService.serverGroupCount == undefined) {
                    return deferred.reject();
                }

                if ($scope.ribbonService.serverGroupCount == 1) {
                    $scope.authTokens[0] = authenticationService.getCurrentToken();
                    return deferred.resolve();
                }

                for (let i = 1; i <= $scope.ribbonService.serverGroupCount; i++) {
                    if ($scope.ribbonService.serverId && $scope.ribbonService.serverId.includes(i.toString())) {
                        $scope.authTokens[i - 1] = authenticationService.getCurrentToken();
                        continue;
                    }

                    if (!$scope.ribbonService.isShared) {
                        var srv = "srv-" + i;
                        var url_str = "https://" + $window.location.hostname + "/" + srv + "/hyperstream/api/tokens?";

                        $scope.authRequests.push(
                            $http({
                                method: "POST",
                                url: url_str,
                                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                                data: $.param($rootScope.qValue)
                            })
                            .then(function (response) {
                                $scope.authTokens[i - 1] = response.data.authToken;
                            })
                            .catch(function (response) {
                                console.log("get authTokens failed: ", response);
                            })
                        );
                    }
                    else {
                        $scope.authTokens[i - 1] = "";
                    }
                }

                if (!$scope.ribbonService.isShared) {
                    $q.all($scope.authRequests)
                    .then(function () {
                        deferred.resolve();
                    });
                }

                return deferred.promise;
            }

            /**
             * Get server-group-count parameter from Guacamole server.
             *
             * There may be several servers behind the load balancer. This method returns
             * the total number of servers behind the load balancer.
             */
            function getServerGroupCount() {
                var deferred = $q.defer();

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                $rootScope.id = clientIdentifier.id;

                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                    name: 'server-group-count'
                };

                $http({
                    method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/property',
                    params: httpParameters
                })
                .then(function (response) {
                    console.debug("Server Group COUNT = " + response.data["server-group-count"]);

                    if (isNaN(response.data["server-group-count"])) {
                        $scope.ribbonService.serverGroupCount = 0;
                    }
                    else {
                        $scope.ribbonService.serverGroupCount = response.data["server-group-count"];
                    }

                    deferred.resolve($scope.ribbonService.serverGroupCount);
                })
                .catch(function (response) {
                    console.error("Error getting server group count:");
                    console.error(response);
                });

                return deferred.promise;
            }

            /**
             * Function that retrieves server ID information from server.
             *
             * There may be several servers behind the load balancer. This method
             * returns the id of the server where the current session is running.
             *
             * Server IDs are in the format of srv-<number>. For example, srv-1, srv-2 etc.
             */
            function getServerId() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                    name: 'server-id'
                };

                return $http({
                    method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/property',
                    params: httpParameters
                })
                .then(function (response) {
                    console.debug("Server ID = " + response.data["server-id"]);
                    $scope.ribbonService.serverId = response.data["server-id"] || "";
                    $rootScope.usb_gateway_token = $scope.ribbonService.serverId;
                })
                .catch(function (response) {
                    console.error("Error getting server ID.");
                });
            }

            function hideDialogExcept(dialogName) {
                if (!(dialogName === 'morePopupVisible')) $scope.ribbonService.morePopupVisible = false;
                if (!(dialogName === 'helpCenterPopupVisible')) $scope.ribbonService.helpCenterPopupVisible = false;
                if (!(dialogName === 'settingPopupVisible')) $scope.ribbonService.settingPopupVisible = false;
                if (!(dialogName === 'remoteAppsPopupVisible')) $scope.ribbonService.remoteAppsPopupVisible = false;
                if (!(dialogName === 'networkQualityDialogVisible')) $scope.ribbonService.networkQualityDialogVisible = false;
                if (!(dialogName === 'showDropDown')) $scope.ribbonService.showDropDown = false;
            }


            /**
             * When the window is closed, send close event to server side; call REST
             * method:
             *
             * http://localhost:8080/hyperstream/api/session/ext/encryptedurl-jdbc/onclose?token=<token>&id=<session-id>&datasource=<data-source>
             *
             */
            $rootScope.onCloseWindow = function () {

                $rootScope.isCloseWindows = true;

                // End the thread that receives the signals from other monitors.
                if ($scope.mmonitorEventSource != null || !$routeParams.hasOwnProperty("mm")) {
                    endMMonitorNotification();
                }

                // Call endHighlight and unsubscribe functions of highlightController in order to remove running thread of MessagingResource
                if ($scope.ribbonService.highlightActivated) {
                    $rootScope.endHighlight();
                    $rootScope.unsubscribe();
                }

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                if ($rootScope.win !== undefined) {
                    $rootScope.win.close();
                }

                // If the vc window is opened, close it
                for (var i = 0; i < $scope.vcWin.length; i++) {
                    $scope.vcWin[i].close();
                }

                // If the 2nd monitor is opened, close it
                if ($rootScope.mmonitor2Win) {
                    for (var i = 0; i < $rootScope.mmonitor2Win.length; i++) {
                        if ($rootScope.mmonitor2Win[i] != null)
                            $rootScope.mmonitor2Win[i].close();
                        $rootScope.mmonitor2Win.splice(i, 1);
                    }
                }

                // If the 3rd monitor is opened, close it
                if ($rootScope.mmonitor3Win) {
                    for (var i = 0; i < $rootScope.mmonitor3Win.length; i++) {
                        if ($rootScope.mmonitor3Win[i] != null)
                            $rootScope.mmonitor3Win[i].close();
                        $rootScope.mmonitor3Win.splice(i, 1);
                    }
                }

                // If the 2nd or 3rd monitor is opened, reset the position of application windows
                // on the primary monitor
                if ($scope.ribbonService.isOpenSecondMonitor || $scope.ribbonService.isOpenThirdMonitor) {
                    $rootScope.resetDesktop();
                }

                // Download log file only when the admin wants the logging.
                if (adminController.enableLogging == true) {
                    const blob = new Blob([window.metrics], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    const now = new Date();
                    link.href = url;
                    link.download = 'metric-log-' + now.toISOString() + '.txt';
                    link.click();
                }

                // Reset the flags indicating whether the 2nd or 3rd monitor is opened or not
                $scope.ribbonService.isOpenSecondMonitor = false;
                $scope.ribbonService.isOpenThirdMonitor = false;

                // Call the proper APIs before closing the browser
                if (datasource === "encryptedurl-jdbc") {
                    // Call the close API
                    var status = $scope.client ? $scope.client.clientState.statusCode : "";
                    var errorName = (status in CLIENT_ERRORS) ? status.toString(16).toUpperCase() : "DEFAULT";

                    if (errorName != "209") {
                        var params = "?token=" + authenticationService.getCurrentToken();
                        params = params + "&id=" + clientIdentifier.id;
                        navigator.sendBeacon("api/session/ext/encryptedurl-jdbc/onclose" + params);
                    }

                    // Stop the thumbnail service
                    thumbnailService.stop();
                    params = "?id=" + clientIdentifier.id + "&delay=2000";
                    navigator.sendBeacon("api/ext/encryptedurl-jdbc/delay" + params);
                }

                if ($rootScope.isKioskMode) {
                    $window.onbeforeunload = null;
                    $rootScope.goLoginPage();
                }

                if ($scope.oldOnUnload)
                    return $scope.oldOnUnload()
                else
                    return '';
            }

            $window.onpagehide = function (event) {
                $rootScope.onCloseWindow();
            }

            // Warn user about refresh or close
            $window.onbeforeunload = function () {

                // If the current page is a shared session
                if ($routeParams.hasOwnProperty("key")) {
                    return;
                }

                // If the current page is a virtual classroom
                if ($location.path().indexOf("classroom") > -1) {
                    return;
                }

                if ($rootScope.onCloseWindowsForPresenter !== undefined) {
                    $rootScope.onCloseWindowsForPresenter();
                }

                // Get the client
                var managedClient = $scope.client;
                if (!managedClient) {
                    return;
                }

                // Get current connection state
                var connectionState = managedClient.clientState.connectionState;

                // If isn't connected, return
                if (connectionState != ManagedClientState.ConnectionState.CONNECTED) {
                    return;
                }

                // If connected, show alert
                return "You have some unsaved changes.";
            };

            $window.onunload = function () {
                if($scope.ribbonService.innerFileBrowserVisible&&$scope.ribbonService.innerFileBrowserUrl){
                   $scope.fileBrowserService.logoutFB($scope.ribbonService.innerFileBrowserUrl)
                }
            }

            /**
             * Shows/Hides assignment manager screen
             */
            $scope.toggleAssignmentManage = function toggleAssignmentManage() {
                    // Google Tag Manager
                dataLayer.push({
                    event: 'Publish assignment',
                    button_name: 'btn_ribbon_lti',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.getAssignmentList();
            }

            /**
             * Shows/Hides assignment publish dialog
             */
            $scope.toggleAssignmentPublish = function toggleAssignmentPublish() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Publish assignment',
                    button_name: 'btn_ribbon_lti',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.getAssignmentList();
            }

            var lastOpenedFileBrowser = 0;

            $scope.fileBrowserDefault = function() {
                $scope.fileBrowser(lastOpenedFileBrowser);
            }

            $scope.fileBrowser = function fileBrowser(index) {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Open Filebrowser',
                    button_name: 'btn_ribbon_open_filebrowser',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.fileBrowserService.getFileBrowserUrl()
                .then(
                    function(url) {
                        if (index === 1)
                            $window.open(url, '_blank');
                        else {
                            $scope.ribbonService.innerFileBrowserUrl = url;
                            $scope.ribbonService.innerFileBrowserVisible = true;
                        }
                        lastOpenedFileBrowser = index;
                    },
                    $scope.fileBrowserService.showFileBrowserErrorDialog
                );
            }

            window.addEventListener('message', (event) => {
                if (event.origin.indexOf($scope.ribbonService.innerFileBrowserUrl) != -1) {
                    if (event.data.action === 'uploadClicked') {
                        console.log('Upload button clicked inside iframe');
                        // Handle the upload button click event
                        // Google Tag Manager
                        dataLayer.push({
                            event: 'Filebrowser upload',
                            button_name: 'btn_ribbon_filebrowser_upload',
                            sub_domain: $scope.ribbonService.licenses.subdomain
                        });
                    }
                    else if (event.data.action === 'downloadClicked') {
                        console.log('Download button clicked inside iframe');
                        // Handle the download button click event
                        // Google Tag Manager
                        dataLayer.push({
                            event: 'Filebrowser download',
                            button_name: 'btn_ribbon_filebrowser_download',
                            sub_domain: $scope.ribbonService.licenses.subdomain
                        });
                    }
                }
            });

            function isValidUrl(url) {
                try {
                    new URL(url);
                    return true;
                } catch (e) {
                    return false;
                }
            }

            /**
             * function to open Portia chatbot
             */
            $scope.openChatbot = function openChatbot() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Open Portia',
                    button_name: 'btn_ribbon_open_portia',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                if (isValidUrl($scope.ribbonService.chatbotUrl)) {
                    $scope.ribbonService.chatbotVisible = true;
                    $timeout(function() {
                        placeChatbot();
                    }, 50);
                } else {
                    console.error("Chatbot URL is invalid: ", $scope.ribbonService.chatbotUrl);
                }
            }

            /**
             * function to close Portia chatbot
             */
            $scope.closeChatbot = function closeChatbot() {
                $scope.ribbonService.chatbotVisible = false;
            }

            /**
             * function places chat bot at correct place i.e. middle right of the window
             */
            function placeChatbot() {
                const chatbotWindow = document.getElementById("chatbotWindow");
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                const windowHeight = chatbotWindow.offsetHeight;
                const windowWidth = chatbotWindow.offsetWidth;
                
                // Check if chat elements exist and are visible
                const kurentoElement = document.querySelector('#kurento');
                const chattingVisible = kurentoElement && kurentoElement.classList.contains('shown');
                
                // base offset from bottom
                const bottomOffset = 45;
                
                // Calculate right offset based on chat state
                let rightOffset = 10;
                if (chattingVisible) {
                    // If chat is minimized, use smaller offset (110px from roomStyle function)
                    // If chat is expanded, use full width (380px from roomStyle function)
                    rightOffset += $scope.ribbonService.chattingMinimized ? 110 : 380;
                }

                // Set position at bottom right
                chatbotWindow.style.top = `${viewportHeight - windowHeight - bottomOffset}px`;
                chatbotWindow.style.left = `${viewportWidth - windowWidth - rightOffset}px`;
            }

            /**
             * Shows/Hides File Explorer dialog
             */
            $scope.toggleFileDownload = function toggleFileDownload() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'File download',
                    button_name: 'btn_ribbon_download',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.fileExplorerVisible = !$scope.ribbonService.fileExplorerVisible;
                $scope.ribbonService.fileDownloadVisible = !$scope.ribbonService.fileDownloadVisible;
                $scope.circleLoaderService.circleLoaderVisible = true;
            }

            /**
             * When the user clicks on the upload icon on ribbon bar, set the flag for uploading files.
             */
            $scope.toggleFileUpload = function toggleFileUpload() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'File upload',
                    button_name: 'btn_ribbon_upload',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.fileUploadVisible = false;
            }

            /**
             * Shows/Hides Session Manager dialog
             */
            $scope.toggleSnapshotManager = function toggleSessionManager() {
                $scope.ribbonService.snapshotManagerVisible = !$scope.ribbonService.snapshotManagerVisible;
            }

            /**
             * Shows/Hides Session Manager dialog
             */
            $scope.toggleVmManager = function toggleVmManager() {
                if ($scope.ribbonService.licenses.cloudProvider == "azure" || $scope.ribbonService.licenses.cloudProvider == "aws") {
                    $scope.circleLoaderService.circleLoaderVisible = false;
                    $scope.ribbonService.vmManagerVisible = !$scope.ribbonService.vmManagerVisible;
                }
                else {
                    $translate('DIALOGS.VM_NOT_CONFIGURED').then(function setError(text) {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = text;
                        $scope.infoService.infoDialogVisible = true;
                    });
                }
            }

            /**
             * Toggle the Help Center menu
             */
            $scope.toggleHelpCenterPopup = function toggleHelpCenterPopup() {
                // Determine the position of popup menu
                var questionButton = document.getElementById("ribbonHelpCenterBtn");
                var HelpCenterPopup = document.getElementById("ribbonHelpCenterPopup");

                HelpCenterPopup.style.right = (window.innerWidth - questionButton.getBoundingClientRect().right ) + 'px';
                HelpCenterPopup.style.top = questionButton.offsetTop + questionButton.offsetHeight + 'px';

                // Toggle the popup menu
                $scope.ribbonService.helpCenterPopupVisible = !$scope.ribbonService.helpCenterPopupVisible;
                hideDialogExcept('helpCenterPopupVisible');
            }
              /**
             * Toggle the Network menu
             */
            $scope.toggleNetworkPopUp = function() {
                // Toggle the visibility of the network quality dialog
                $scope.ribbonService.networkQualityDialogVisible = !$scope.ribbonService.networkQualityDialogVisible;

                // Determine the position of the popup menu
                var networkPopUp = document.getElementById("networkPopUp");
                var networkPopUpBtn = document.getElementById("network-indicator-btn");

                // Check if the elements exist before attempting to manipulate their styles
                if (networkPopUp && networkPopUpBtn) {
                    networkPopUp.style.left = (networkPopUpBtn.getBoundingClientRect().left - networkPopUp.getBoundingClientRect().width + networkPopUpBtn.getBoundingClientRect().width) + 'px';
                    networkPopUp.style.top = networkPopUpBtn.offsetTop + networkPopUpBtn.offsetHeight + 'px';
                }

                // Close other popups
                hideDialogExcept('networkQualityDialogVisible');
            };

            /**
             * Monitor the array of virtual classroom windows. If user closes any of the windows,
             * remove it from the array.
             */
            function watchVCWindow() {
                var i = 0;
                while ( i < $scope.vcWin.length) {
                    if ($scope.vcWin[i].closed) {
                        $scope.vcWin.splice(i, 1);
                        continue;
                    }
                    i++;
                }

                if ($scope.vcWin.length == 0) {
                    $interval.cancel(idleWatchVCWin);
                }
            }

            /**
             * Shows/Hides Classrrom Group Picker; triggered by click on the ribbon button
             * */
            $scope.ribbonService.toggleClassroom = function toggleClassroom() {
                // if there is only one group do not show group chooser
                if ($scope.ribbonService.classroomGroups != null && $scope.ribbonService.classroomGroups.length > 1) {
                    // Sort groups and displey them in the dropdown list.
                    $scope.ribbonService.classroomGroups.sort();
                    $rootScope.selectedGroup = $scope.ribbonService.classroomGroups[0];
                    $scope.ribbonService.classroomDialogVisible = true;
                }
            }

            $rootScope.displayClassroomView = false;
            
            /**
             * Opens the virtual classroom window.
             *
             * The virtual classroom have to display thumbnails of all students.
             * However, the students sessions may be scattered across multiple servers behind load balancer.
             * Therefore, we need to know names and IDs of all servers behind load balancer, so we can query
             * students on each server.
             */
            $rootScope.openClassroom = function openClassroom() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Virtual classroom',
                    button_name: 'btn_ribbon_classroom',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $rootScope.displayClassroomView = true;
                if ($scope.authTokens.length <= 0) {
                    $("body").css("cursor", "progress");

                    $q.all([
                            getServerId(),
                            getServerGroupCount(),
                            getMessengerHostname(),
                            userInfoService.getUserInfo(),
                        ])
                        .then(getAuthTokens)
                        .then(function () {
                            if ($scope.ribbonService.serverId == "" || !$scope.ribbonService.serverId) {
                                $rootScope.authToken = 1;
                            }
                            else {
                                $rootScope.authToken = $scope.ribbonService.serverId.substr(-1);
                            }

                            if ($scope.authTokens.length > 3)
                                $rootScope.srv4 = $scope.authTokens[3];
                            if ($scope.authTokens.length > 2)
                                $rootScope.srv3 = $scope.authTokens[2];
                            if ($scope.authTokens.length > 1)
                                $rootScope.srv2 = $scope.authTokens[1];
                            if ($scope.authTokens.length > 0)
                                $rootScope.srv1 = $scope.authTokens[0];

                            $scope.vcWin.push(
                                window.open(
                                    location.origin + location.pathname + '#/classroom/' + $rootScope.id + '/' +
                                    window.btoa($rootScope.selectedGroup) + '/' + window.btoa($scope.ribbonService.licenses.appname) + '/' +
                                    $rootScope.authToken + '/' +
                                    ($rootScope.srv1 == undefined ? '_' : $rootScope.srv1) + '/' +
                                    ($rootScope.srv2 == undefined ? '_' : $rootScope.srv2) + '/' +
                                    ($rootScope.srv3 == undefined ? '_' : $rootScope.srv3) + '/' +
                                    ($rootScope.srv4 == undefined ? '_' : $rootScope.srv4)
                                )
                            );

                            $("body").css("cursor", "default");

                            if ($scope.ribbonService.licenses.hasMessengerLicence) {
                                var roomName = $scope.ribbonService.GLOBAL_ROOM_NAME;
                                if (!ServiceRoom.getKurento(roomName)) {
                                    $q.all([
                                        getUserList(),
                                    ])
                                    .then(function () {
                                        if ($scope.ribbonService.licenses.hasChattingLicence && $scope.ribbonService.userlist && $scope.ribbonService.userlist.length != 0) {
                                            register();
                                        }
                                    })
                                }
                            }
                        });
                }
                else {
                    $scope.vcWin.push(
                        window.open(
                            location.origin + location.pathname + '#/classroom/' + $rootScope.id + '/' +
                            window.btoa($rootScope.selectedGroup) + '/' + window.btoa($scope.ribbonService.licenses.appname) + '/' +
                            $rootScope.authToken + '/' +
                            ($rootScope.srv1 == undefined ? '_' : $rootScope.srv1) + '/' +
                            ($rootScope.srv2 == undefined ? '_' : $rootScope.srv2) + '/' +
                            ($rootScope.srv3 == undefined ? '_' : $rootScope.srv3) + '/' +
                            ($rootScope.srv4 == undefined ? '_' : $rootScope.srv4)
                        )
                    );
                }

                idleWatchVCWin = $interval(watchVCWindow, SECOND_UNIT);

                $rootScope.closeClassroomDialog();

                $timeout(function() {
                    if ($rootScope.isClassroom)
                        $rootScope.displayClassroomView = true;
                    else
                        $rootScope.displayClassroomView = false;
                }, CHECK_VC_WINDOW);

            }

            /**
             * Shows/Hides Presenter Group Picker
             */
            $scope.ribbonService.togglePresenter = function togglePresenter() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Presenter mode',
                    button_name: 'btn-presenter',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $q.all([
                    getAuthTokens(),
                ])
                .then(function() {
                    if ($scope.ribbonService.serverId == "" || !$scope.ribbonService.serverId) {
                        $rootScope.authToken = 1;
                    }
                    else {
                        $rootScope.authToken = $scope.ribbonService.serverId.substr(-1);
                    }

                    if ($scope.authTokens.length > 3)
                        $rootScope.srv4 = $scope.authTokens[3];
                    if ($scope.authTokens.length > 2)
                        $rootScope.srv3 = $scope.authTokens[2];
                    if ($scope.authTokens.length > 1)
                        $rootScope.srv2 = $scope.authTokens[1];
                    if ($scope.authTokens.length > 0)
                        $rootScope.srv1 = $scope.authTokens[0];
                });

                // if there is only one group do not show group chooser
                if ($scope.ribbonService.presenterVisible &&
                    $scope.ribbonService.presenterGroups !== null &&
                    $scope.ribbonService.presenterGroups.length > 0) {
                    $scope.ribbonService.presenterGroups.sort();
                    $scope.ribbonService.presenterDialogVisible = true;
                }
                else if ($scope.ribbonService.isPresenter) {
                    $rootScope.$broadcast('presenterMode:start');
                    $scope.ribbonService.activeMembers = [];
                }
            }

            $rootScope.$on("updateClosedGroupList", function () {
                // Remove closed groups from the array of opened groups, and add them back to possible groups.
                if ($rootScope.closedGroups && $rootScope.closedGroups.length > 0) {
                    $rootScope.closedGroups.forEach(element => {
                        if ($scope.ribbonService.classroomGroups.indexOf(element) < 0 && $rootScope.openedGroups.indexOf(element) >= 0) {
                            $scope.ribbonService.classroomGroups.push(element);
                            $rootScope.openedGroups.splice($rootScope.openedGroups.indexOf(element), 1);
                        }
                    });
                    $rootScope.closedGroups = [];
                }

                if($scope.ribbonService.classroomGroups.length >= 1){
                    $rootScope.selectedGroup = $scope.ribbonService.classroomGroups[0];
                }
            });

            $rootScope.$on("updateOpenedGroupList", function () {
                // Remove opened groups from the array of possible groups, if they are there.
                if ($rootScope.openedGroups && $rootScope.openedGroups.length > 0) {
                    $rootScope.openedGroups.forEach(element => {
                        if ($scope.ribbonService.classroomGroups.indexOf(element) >= 0) {
                            $scope.ribbonService.classroomGroups.splice($scope.ribbonService.classroomGroups.indexOf(element), 1);
                        }
                    });
                }

                if($scope.ribbonService.classroomGroups.length >= 1){
                    $rootScope.displayClassroomView = false;
                    $rootScope.isClassroom = false;
                    $rootScope.selectedGroup = $scope.ribbonService.classroomGroups[0];
                }
            });

            $scope.ribbonService.handRaise = function handRaise() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };
                var req = {
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/classroom/raisehand",
                    params: httpParameters,
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                };

                $http(req).then(function () {
                    $rootScope.visibleClassroomView = false;
                    $rootScope.handRaised = true;

                    var notify = new XMLHttpRequest();
                    var waitResponse = $interval(function () {
                        var responseRequest = '?id=' + encodeURIComponent(clientIdentifier.id) +
                                              "&token=" + encodeURIComponent(authenticationService.getCurrentToken());
                        notify.open('GET', 'api/session/ext/encryptedurl-jdbc/classroom/checkhand' + responseRequest);
                        notify.send();

                        notify.onreadystatechange = function () {
                            if (notify.readyState === 4) {
                                console.log(notify.response);
                                if (notify.response === "REMOVED") {
                                    var message_id;
                                    if ($scope.ribbonService.userinfo.roles === 'Faculty Admin') {
                                        message_id = 'CLASSROOM.COLLEAGUE_ACKNOWLEDGED_HAND_RAISE';
                                    }
                                    else {
                                        message_id = 'CLASSROOM.FACULTY_ACKNOWLEDGED_HAND_RAISE';
                                    }

                                    $translate(message_id).then(function (message) {
                                        $scope.infoService.infoText = message;

                                        if (!$scope.student) {
                                            $scope.infoService.infoDialogVisible = true;
                                            $scope.infoService.top = true;
                                            $timeout(function hideInfo() {
                                                $scope.infoService.infoDialogVisible = false;
                                            }, $scope.ribbonService.thresholdInfo);
                                        }
                                        else {
                                            $scope.student = false;
                                        }
                                    });

                                    $interval.cancel(waitResponse);
                                    $rootScope.visibleClassroomView = true;
                                    $rootScope.handRaised = false;
                                }
                            }
                        }
                    }, 3000);
                }).catch(function () {
                }).finally(function () {
                    console.debug("Raised hand");
                });
            }

            $scope.ribbonService.removeHand = function removeHand() {
                $rootScope.handRaised = false;
                $rootScope.visibleClassroomView = true;
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };
                var req = {
                    method: 'DELETE',
                    url: "api/session/ext/" + datasource + "/classroom/removehand",
                    params: httpParameters
                };

                $http(req).then(function () {
                    $scope.student = true;
                }).catch(function () {
                }).finally(function () {
                    console.debug("Removed raised hand");
                });
            }

            morePopupList = [
                '.btn-classroom-mandatory',
                '.btn-mmonitor',
                '.btn-backup'
            ]
            settingPopupList = [
                '.btn-vm'
            ]

            // Check if there are the menus in the more and setting popup.
            function checkExistance(list) {
                if (list.length > 0) {
                    for (let element of list) {
                        if (document.querySelector(element)) {
                            return true;
                        }
                    }
                }
                return false;
            }

            /**
             * Show the introduction popup.
             */
            function showIntroPopup() {

                // Show the ribbon bar when showing the intro popup
                if ($scope.hideRibbonBar)
                    $scope.hideRibbonBar = false;

                /******* IntroJS *******/
                // options for tour steps
                $scope.IntroOptions = {
                    // Indicates whether intro will be shown later
                    saveForLater: false,

                    steps: [
                        {// cloud mounter existing users
                            title: "cloud mounter",
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.CLOUD_MOUNTER') + "</strong></p>" + $translate.instant('INTRO.CLOUD_MOUNTER'),
                            updates: true
                        },
                        {// full screen
                            title: "full screen",
                            element: document.querySelector('.btn-full-screen'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.FULL_SCREEN') + "</strong></p>" + "<p>" + $translate.instant('INTRO.FULL_SCREEN_1') + "</p>" + $translate.instant('INTRO.FULL_SCREEN_2'),
                            position: 'bottom',
                            updates: false
                        },
                        {// full screen
                            title: "full screen",
                            element: document.querySelector('.btn-minimize-screen'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.FULL_SCREEN') + "</strong></p>" + "<p>" + $translate.instant('INTRO.FULL_SCREEN_1') + "</p>" + $translate.instant('INTRO.FULL_SCREEN_2'),
                            position: 'bottom',
                            updates: false
                        },
                        {// file upload button
                            title: "upload",
                            element: document.querySelector('.btn-file-upload'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.UPLOAD') + "</strong></p>" + $translate.instant('INTRO.UPLOAD'),
                            position: 'bottom',
                            updates: false
                        },
                        {// file download button
                            title: "download",
                            element: document.querySelector('.btn-file-download'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.DOWNLOAD') + "</strong></p>" + $translate.instant('INTRO.DOWNLOAD'),
                            position: 'bottom',
                            updates: false
                        },
                        (!$scope.ribbonService.licenses.hasMacOSLicence && !$scope.ribbonService.licenses.hasLinuxLicence) ? (
                            {// cloud mounter existing users
                                title: "cloud mounter for existing users",
                                intro: "<p><strong>" + $translate.instant('INTRO_TITLE.CLOUD_MOUNTER') + "</strong><p>" + $translate.instant('INTRO.CLOUD_MOUNTER_FOR_USERS') +
                                       "<img src='app/ext/ribbon/images/cloudMounter.png' style='display: block; margin: auto; height: 130px; width: auto'>",
                                updates: true
                            },
                            {// cloud mounter new users
                                title: "cloud mounter for new users",
                                intro: "<p><strong>" + $translate.instant('INTRO_TITLE.CLOUD_MOUNTER') + "</strong><p>" + "<p>" + $translate.instant('INTRO.CLOUD_MOUNTER_FOR_USERS') +
                                       "<img src='app/ext/ribbon/images/cloudMounter.png' style='display: block; margin: auto; height: 130px; width: auto'>",
                                updates: false
                            }
                        ) : null,
                        {// btn share
                            title: "share",
                            element: document.querySelector('.btn-share'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SHARE') + "</strong></p>" + $translate.instant('INTRO.SHARE'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn restart MacOS / Linux Session
                            title: "restart mac / linux",
                            element: document.querySelector('.btn-reset-non-window-session'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.RESTART_MAC_LINUX') + "</strong></p>" + $translate.instant('INTRO.RESTART_MAC_LINUX'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn restart MacOS / Linux Session
                            title: "restart mac / linux",
                            element: document.querySelector('.btn-reset-non-window-session'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.RESTART_MAC_LINUX') + "</strong></p>" + $translate.instant('INTRO.RESTART_MAC_LINUX'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn publish
                            title: "publish to LMS",
                            element: document.querySelector('.btn-assignment-manage'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.MANAGE_TO_LMS') + "</strong></p>" + $translate.instant('INTRO.MANAGE_TO_LMS'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn publish
                            title: "publish to LMS",
                            element: document.querySelector('.btn-assignment-manage'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.MANAGE_TO_LMS') + "</strong></p>" + $translate.instant('INTRO.MANAGE_TO_LMS'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn classroom
                            title: "classroom",
                            element: document.querySelector('.btn-classroom-mandatory'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.CLASSROOM') + "</strong></p>" + $translate.instant('INTRO.CLASSROOM'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn classroom
                            title: "classroom",
                            element: document.querySelector('.btn-classroom-mandatory'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.CLASSROOM') + "</strong></p>" + $translate.instant('INTRO.CLASSROOM'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn presenter
                            title: "presenter",
                            element: document.querySelector('.btn-presenter'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.PRESENTER') + "</strong></p>" + $translate.instant('INTRO.PRESENTER'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn presenter
                            title: "presenter",
                            element: document.querySelector('.btn-presenter'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.PRESENTER') + "</strong></p>" + $translate.instant('INTRO.PRESENTER'),
                            position: 'bottom',
                            updates: false
                        },
                        {// multi-monitor btn
                            title: "multi monitor",
                            element: document.querySelector('.btn-mmonitor'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.MULTI_MONITOR') + "</strong></p>" + $translate.instant('INTRO.MULTI_MONITOR'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn backup / restore
                            title: "backup / restore",
                            element: document.querySelector('.btn-backup'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.BACKUP_RESTORE') + "</strong></p>" + $translate.instant('INTRO.BACKUP_RESTORE'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn backup / restore
                            title: "backup / restore",
                            element: document.querySelector('.btn-backup'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.BACKUP_RESTORE') + "</strong></p>" + $translate.instant('INTRO.BACKUP_RESTORE'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn full network
                            title: "network indicator",
                            element: document.querySelector('.ribbon-network-indicator'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.NETWORK_INDICATOR') + "</strong></p>" + $translate.instant('INTRO.NETWORK_INDICATOR'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn full network
                            title: "network indicator",
                            element: document.querySelector('.ribbon-network-indicator'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.NETWORK_INDICATOR') + "</strong></p>" + $translate.instant('INTRO.NETWORK_INDICATOR'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn sound
                            title: "sound",
                            element: document.querySelector('.btn-sound'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SOUND') + "</strong></p>" + $translate.instant('INTRO.SOUND'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn help
                            title: "help",
                            element: document.querySelector('.btn-help'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.HELP') + "</strong></p>" + $translate.instant('INTRO.HELP'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn help
                            title: "help",
                            element: document.querySelector('.btn-help'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.HELP') + "</strong></p>" + $translate.instant('INTRO.HELP'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn settings
                            title: "settings",
                            element: document.querySelector('.btn-gear'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SETTINGS') + "</strong></p>" + $translate.instant('INTRO.SETTINGS'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn settings
                            title: "settings",
                            element: document.querySelector('.btn-gear'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SETTINGS') + "</strong></p>" + $translate.instant('INTRO.SETTINGS'),
                            position: 'bottom',
                            updates: false
                        },
                        // Not use
                        {// side-menu btn
                            title: "side menu",
                            element: document.querySelector('.btn-side-menu'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SIDE_MENU') + "</strong></p>" + $translate.instant('INTRO.SIDE_MENU'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn VM reboot
                            title: "reboot VM",
                            element: document.querySelector('.btn-vm'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.REBOOT_VM') + "</strong></p>" + $translate.instant('INTRO.REBOOT_VM'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn VM reboot
                            title: "reboot VM",
                            element: document.querySelector('.btn-vm'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.REBOOT_VM') + "</strong></p>" + $translate.instant('INTRO.REBOOT_VM'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn session time
                            title: "session time",
                            element: document.querySelector('.ribbon-session-time'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SESSION_TIME') + "</strong></p>" + $translate.instant('INTRO.SESSION_TIME'),
                            position: 'bottom',
                            updates: true
                        },
                        {// btn session time
                            title: "session time",
                            element: document.querySelector('.ribbon-session-time'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.SESSION_TIME') + "</strong></p>" + $translate.instant('INTRO.SESSION_TIME'),
                            position: 'bottom',
                            updates: false
                        },
                        {// btn stats
                            title: "stats",
                            element: document.querySelector('.ribbon-stats'),
                            intro: "<p><strong>" + $translate.instant('INTRO_TITLE.STATS') + "</strong></p>" + $translate.instant('INTRO.STATS'),
                            position: 'bottom',
                            updates: false
                        }
                    ],
                    showStepNumbers: false,
                    showBullets: false,
                    exitOnOverlayClick: false,
                    exitOnEsc: false,
                    nextLabel: $translate.instant('INTRO.BUTTON_NEXT'),
                    prevLabel: $translate.instant('INTRO.BUTTON_PREVIOUS'),
                    skipLabel: '<span style="color: white;">' + $translate.instant('INTRO.BUTTON_EXIT') + '</span>',
                    doneLabel: '<span style="color: white;">' + $translate.instant('INTRO.BUTTON_DONE') + '</span>'
                };

                // given callback function will be called after completing each step
                // the callback function receives the element
                // of the next step as an arguments
                ngIntroService.onChange(function (element) {

                    // If the next element is share button
                    // close file explorer dialog.
                    if (element.classList.contains('btn-share')) {
                        $scope.ribbonService.ribbonShareDialogVisible = false;
                    }

                    // If the next element is share dialog and it is not visible
                    // click share button to open it.
                    if (element.classList.contains("share-dialog")) {
                        $scope.ribbonService.ribbonShareDialogVisible = true
                    }

                    if (ngIntroService.intro._currentStep == ($scope.IntroOptions.steps.length - 1)) {
                        $('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton').css('display', 'none');
                        $('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton').css('border-right', '3px;');
                        $('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton').css('border-radius', '3px');
                    }

                    // Removed prev button
                    var prevbutton = $('.introjs-button.introjs-prevbutton');
                    prevbutton.remove();

                    setTimeout(() => {
                        var prevButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton');
                        var nextButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton');
                        var skipButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-skipbutton');
                        var tooltipTextLayer = document.querySelector('.introjs-tooltip .introjs-tooltiptext');

                        prevButton && prevButton.setAttribute("tabindex", "0");
                        nextButton && nextButton.setAttribute("tabindex", "0");
                        skipButton && skipButton.setAttribute("tabindex", "0");

                        skipButton && skipButton.addEventListener('focus', () => {
                            $('.introjs-tooltip .introjs-tooltiptext').css('outline', 'none');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton').css('outline', 'none');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-skipbutton').css('outline', '2px solid #000');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-skipbutton').css('background-image', 'linear-gradient(rgba(26,87,158,0.8), rgba(26,87,158,0.8))');
                        });

                        nextButton && nextButton.addEventListener('focus', () => {
                            $('.introjs-tooltip .introjs-tooltiptext').css('outline', 'none');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-skipbutton').css('outline', 'none');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton').css('outline', '2px solid #000');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton').css('background-image', 'linear-gradient(rgba(26,87,158,0.8), rgba(26,87,158,0.8))');
                        });

                        tooltipTextLayer && tooltipTextLayer.addEventListener('focus', () => {
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-skipbutton').css('outline', 'none');
                            $('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton').css('outline', 'none');
                        });

                        $rootScope.setFocusableElements(".introjs-tooltip");

                        // Highlight feature
                        $('.introjs-helperLayer').css('border', '2px solid #007bff');
                        $('.introjs-helperLayer').css('border-radius', '4px');

                    }, 0);

                });

                // If the tour is exited, close chart dialog and share dialog
                ngIntroService.onExit(function () {
                    hideAllDialogs()
                    saveLaterInfo();

                    // Hide the ribbon bar if the fullscreen mode
                    if ($scope.ribbonService.isFullScreen && !$scope.hideRibbonBar) {
                        $timeout(function() {
                            $scope.hideRibbonBar = true;
                        }, 1000);
                    }

                    // Hide opened sub menu
                    if (document.querySelector("#ribbonMore") && checkExistance(morePopupList)) {
                        document.querySelector("#ribbonMore").click();
                    }
                    if (checkExistance(settingPopupList)) {
                        document.querySelector("#ribbonSettingBtn").click();
                    }
                    $scope.ribbonService.introRunning = false;
                });

                // If there is intro parameter with value of 1, start intro
                if (typeof $routeParams.intro !== 'undefined' && $routeParams.intro === "1" &&
                    !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua))) {
                    $scope.ribbonService.introRunning = true;

                    /**
                     * HTML Elements with ng-if directive do not exists during creation
                     * of IntroOptions. Update elements that are activated when introRunning is set to true.
                     */
                    $timeout(function () {
                        // Removed intro steps for existing users
                        $scope.IntroOptions.steps = $scope.IntroOptions.steps.filter((step) => step != null && step.element !== null);

                        // Removed intro steps for existing users
                        $scope.IntroOptions.steps = $scope.IntroOptions.steps.filter((step) => step != null && step.updates === false);

                        // set options for the intro tour
                        ngIntroService.setOptions($scope.IntroOptions);
                        ngIntroService.start();

                        // Click the more button to intro
                        if (checkExistance(settingPopupList)) {
                            document.querySelector("#ribbonSettingBtn").click();
                        }
                        if (document.querySelector("#ribbonMore") && checkExistance(morePopupList)) {
                            document.querySelector("#ribbonMore").click();
                        }

                        // Prepand later button in intro tooltip
                        var prevButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton');
                        var nextButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton');
                        prevButton.setAttribute("tabindex", "0");
                        nextButton.setAttribute("tabindex", "0");

                        var tooltipTextLayer = document.getElementsByClassName('introjs-tooltiptext');

                        for (var i = 0; i < tooltipTextLayer.length; i++) {
                            tooltipTextLayer[i].setAttribute('aria-live', 'assertive');
                            tooltipTextLayer[i].setAttribute('role', 'dialog');
                            tooltipTextLayer[i].setAttribute('tabindex', '0');
                        }

                        for (var i = 1; i <= $scope.IntroOptions.steps.length; i++) {
                            /**
                             * Carousel items for ribbon buttons on intro dialog are showing like below by introjs libray
                             * <a class=​"active" role=​"button" tabindex=​"0" data-stepnumber=​"1">​&nbsp;​</a>​
                             */
                            var buttonElement = document.querySelectorAll('[data-stepnumber="' + i + '"]')[0];
                            if (buttonElement)
                                buttonElement.setAttribute("title", $scope.IntroOptions.steps[i-1]["title"]);
                        }

                        introTotal = $scope.IntroOptions.steps.length;

                        // Change style of "Next" button
                        $('.introjs-button.introjs-nextbutton').css('padding', '6px 12px');
                        $('.introjs-button.introjs-nextbutton').css('border', '1px solid #22538f');
                        $('.introjs-button.introjs-nextbutton').css('border-radius', '5px');
                        $('.introjs-button.introjs-nextbutton').css('background-image', 'linear-gradient(#22538f, #22538f)');
                        $('.introjs-button.introjs-nextbutton').css('font', '12px / normal sans-serif');
                        $('.introjs-button.introjs-nextbutton').css('text-shadow', 'none');
                        $('.introjs-button.introjs-nextbutton').css('color', 'white');

                        // Change style of "Exit" button
                        $('.introjs-button.introjs-skipbutton').css('padding', '6px 14px');
                        $('.introjs-button.introjs-skipbutton').css('border', '1px solid #22538f');
                        $('.introjs-button.introjs-skipbutton').css('border-radius', '5px');
                        $('.introjs-button.introjs-skipbutton').css('background-image', 'linear-gradient(#22538f, #22538f)');
                        $('.introjs-button.introjs-skipbutton').css('font', '12px / normal sans-serif');
                        $('.introjs-button.introjs-skipbutton').css('text-shadow', 'none');

                        // Show indicator
                        $('.introjs-tooltipbuttons').css({position:'relative'});
                        $('.introjs-tooltipbuttons').css('margin-top', '20px');
                        $('.introjs-tooltipbuttons').append("<div class='introjs-indicator' style='position: absolute; font-size: 14px; top: 15px;'>" + introIndex + " of " + introTotal + "</div>");

                        function introNext() {
                            introIndex++;
                            $('.introjs-indicator').html(introIndex + " of " + introTotal);
                            $('.introjs-tooltipReferenceLayer').css('margin-top', '8px');
                        }
                        $('.introjs-button.introjs-nextbutton').click(function(){
                            introNext();
                        });
                        $(document).keydown(function(event) {
                            if (event.keyCode === 13) { // Check if the Enter key is pressed
                                introNext();
                            }
                        });

                        // Increase the min width of the message box ("introjs-tooltip")
                        $('.introjs-tooltipReferenceLayer .introjs-tooltip').css('min-width', '280px');
                        $('.introjs-tooltipReferenceLayer .introjs-tooltip').css('margin-top', '5px');
                        $('.introjs-tooltipReferenceLayer .introjs-tooltip').css('border-radius', '8px 8px');
                        $('.introjs-tooltipReferenceLayer').css('margin-top', '8px');

                        // Add the right margin 5px to the "Previous" button
                        $('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton').css('margin-right', '5px');

                    }, 250, false);
                }
                else if ($routeParams.intro === "1" &&
                         /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua)) {
                    $scope.later();
                }

                //if there is updates parameter with value of 1, start intro for old users
                if (typeof $routeParams.updates !== 'undefined' && $routeParams.updates === "1" &&
                    !(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua))) {
                    $scope.ribbonService.introRunning = true;

                    /**
                     * HTML Elements with ng-if directive do not exists during creation
                     * of IntroOptions. Update elements that are activated when introRunning is set to true.
                     */
                    $timeout(function () {
                        // Removed intro steps for existing users
                        $scope.IntroOptions.steps = $scope.IntroOptions.steps.filter((step) => step != null && step.element !== null);

                        // Leave only new functionalities
                        $scope.IntroOptions.steps = $scope.IntroOptions.steps.filter((step) => step != null && step.updates === true);

                        // Set options for the intro tour
                        ngIntroService.setOptions($scope.IntroOptions);
                        ngIntroService.start();

                        // Click the more button to intro
                        if (checkExistance(settingPopupList)) {
                            document.querySelector("#ribbonSettingBtn").click();
                        }
                        if(document.querySelector("#ribbonMore") && checkExistance(morePopupList)) {
                            document.querySelector("#ribbonMore").click();
                        }

                        // Prepand later button in intro tooltip
                        var prevButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-prevbutton');
                        var nextButton = document.querySelector('.introjs-tooltip .introjs-tooltipbuttons .introjs-nextbutton');
                        prevButton.setAttribute("tabindex", "0");
                        nextButton.setAttribute("tabindex", "0");

                        var tooltipTextLayer = document.getElementsByClassName('introjs-tooltiptext');

                        for (var i = 0; i < tooltipTextLayer.length; i++) {
                            tooltipTextLayer[i].setAttribute('aria-live', 'assertive');
                            tooltipTextLayer[i].setAttribute('role', 'dialog');
                            tooltipTextLayer[i].setAttribute('tabindex', '0');
                        }

                    }, 250, false);
                }
                else if ($routeParams.updates === "1" &&
                         /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua)) {
                    $scope.later();
                }
            };

            /**
             * Hides all dialogs possibly opened by intro.
             */
            function hideAllDialogs() {
                $scope.ribbonService.fileExplorerVisible = false
                $scope.ribbonService.fileUploadVisible = false;
                $scope.ribbonService.fileDownloadVisible = false;
                $scope.ribbonService.assignmentPublishVisible = false;
                $scope.ribbonService.filesToUpload = [];

                $scope.ribbonService.summaryChartVisible = false;
                $scope.ribbonService.ribbonShareDialogVisible = false;
            }

            /**
             * Send Drupal info whether intro should be shown again.
             */
            function saveLaterInfo() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = clientIdentifier.dataSource;

                // API for remove later value from drupal
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                };
                if ($scope.IntroOptions.saveForLater) {
                    $http({
                        method: 'PUT',
                        url: "api/session/ext/" + datasource + "/later",
                        params: httpParameters
                    })
                    .then(function (response) {
                    })
                    .catch(function (response) {
                        console.error("Error setting later flag: ", response.message);
                    });
                }
                else {
                    $http({
                        method: 'DELETE',
                        url: "api/session/ext/" + datasource + "/later",
                        params: httpParameters
                    })
                    .then(function (response) {
                    })
                    .catch(function (response) {
                        console.error("Error removing later flag: ", response.message);
                    });
                }
            }

            /**
             * Prevent grabbing keyboard if mouse is over ribbon
             */
            $scope.$on('guacBeforeKeydown', function filterKeydown(event, keysym) {
                // If the user is using a mobile device, ignore the flags related to the ribbon bar
                if ($scope.tablet)
                    return;

                if (!$rootScope.isKioskMode && $scope.ribbonService.ribbonActive || $scope.ribbonService.introRunning || $rootScope.ribbonActive) {
                    event.preventDefault();
                    return false;
                }

                if ($rootScope.isKioskMode) {
                    function preventKeyBoard(event) {
                            event.preventDefault();
                    }

                    window.addEventListener('focus', () => {
                        window.addEventListener('keydown', preventKeyBoard);
                    });

                    return;
                }
            });

            $scope.$on('guacBeforeKeyup', function filterKeyup(event, keysym) {
                // If the user is using a mobile device, ignore the flags related to the ribbon bar
                if ($scope.tablet)
                    return;

                if (!$rootScope.isKioskMode && $scope.ribbonService.ribbonActive || $scope.ribbonService.introRunning || $rootScope.ribbonActive) {
                    event.preventDefault();
                    return false;
                }
            });

            $scope.$on('guacNotification', function showStatus(event, status) {
                if (status) {
                    setTimeout(function () {
                        $rootScope.setFocusableElements('#guacNotification');
                        $rootScope.ribbonActive = true;
                    }, 700);
                    
                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;
                    $rootScope.ribbonActive = false;
                }
            });

            // Track pressed key
            $rootScope.$on('guacCtrlAltKeydown', function () {
                var ribbonBar = document.querySelector("ribbon-bar");
                if (!ribbonBar) return;

                var focusableContent = ribbonBar.querySelectorAll(".ribbon-button:not(.ng-hide)");

                if ($scope.hideRibbonBar) {
                    // Make the ribbon bar visible
                    if(!$scope.isSmallRibbonBarVisible) {
                        $scope.isSmallRibbonBarVisible = true;
                        $timeout(function () {
                            $scope.targetElement = document.getElementById("compressed_ribbon_bar_block");
                            if ($scope.targetElement) {
                                $scope.targetElement.focus();
                            }
                        }, 500);
                    }
                }
                else {
                    // focusableContent.forEach(function (element) {
                    //     element.setAttribute('tabindex', '0');
                    // });

                    if (focusableContent.length > 0) {
                        $scope.targetElement = focusableContent[0];
                        $timeout(function () {
                            if ($scope.targetElement) {
                                $scope.targetElement.focus();
                            }
                        }, 0);
                    }
                }

            });

            var ribbonbarExitChecker = null;
            var ribbonbarExitCounter = 0;
            var ribbonbar = $document[0].querySelector('ribbon-bar');
            if (ribbonbar) {
                ribbonbar && ribbonbar.addEventListener("mouseleave", function (e) {
                    if ($scope.ribbonService.isFullScreen && !$scope.hideRibbonBar) {
                        if (!ribbonbarExitChecker) {
                            ribbonbarExitChecker = $interval(function () {
                                ribbonbarExitCounter ++;
                                if (ribbonbarExitCounter >= 2) {
                                    $scope.hideRibbonBar = true;
                                    $interval.cancel(ribbonbarExitChecker);
                                    ribbonbarExitCounter = 0;
                                    ribbonbarExitChecker = null;
                                }
                            }, 1000);
                        }
                    }
                });
                ribbonbar && ribbonbar.addEventListener("mouseover", function (e) {
                    if ($scope.ribbonService.isFullScreen) {
                        $interval.cancel(ribbonbarExitChecker);
                        ribbonbarExitCounter = 0;
                        ribbonbarExitChecker = null;
                    }
                });
            }

            $document[0].addEventListener('keydown', function (e) {
                let isTabPressed = e.key === 'Tab' || e.keyCode === 9;

                if (!isTabPressed) {
                    return;
                }

                if ($scope.infoService.infoDialogVisible && !($scope.ribbonService.snapshotManagerVisible ||
                    $scope.ribbonService.ribbonShareDialogVisible)) {
                    if (e.shiftKey) { // if shift key pressed for shift + tab combination
                        if ($document[0].activeElement === $rootScope.firstInfoFocusableElement) {
                            $rootScope.lastInfoFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                    else { // if tab key is pressed
                        if ($document[0].activeElement === $rootScope.lastInfoFocusableElement) {
                            $rootScope.firstInfoFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                }
                else {
                    if (e.shiftKey) { // if shift key pressed for shift + tab combination
                        if ($document[0].activeElement === $rootScope.firstFocusableElement) {
                            $rootScope.lastFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                    else { // if tab key is pressed
                        if ($document[0].activeElement === $rootScope.lastFocusableElement) {
                            $rootScope.firstFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                }
            });

            $window.onresize = function (event) {
                if ($scope.isFullScreen()) {
                    $scope.ribbonService.isFullScreen = true;
                    $scope.hideRibbonBar = true;
                }
                else {
                    $scope.ribbonService.isFullScreen = false;
                    $scope.hideRibbonBar = false;

                    $interval.cancel(ribbonbarExitChecker);
                    ribbonbarExitCounter = 0;
                    ribbonbarExitChecker = null;
                }
            }

            /**
             * Enter or exit fullscreen mode
             */
            $scope.toggleFullScreen = async function toggleFullScreen() {

                // root element of the document (<html>)
                var rootElement = document.documentElement;

                // if browser is not in fullscreen, enter fullscreen
                if (!$scope.isFullScreen()) {
                    $scope.isSmallRibbonBarVisible = false;
                    // Google Tag Manager
                    dataLayer.push({
                        event: 'Full screen mode',
                        button_name: 'btn_ribbon_full_screen',
                        sub_domain: $scope.ribbonService.licenses.subdomain
                    });

                    // Check if the Keyboard Lock API is supported
                    if ('keyboard' in navigator && 'lock' in navigator.keyboard) {
                        // Lock the keyboard
                        navigator.keyboard.lock();
                    }

                    // Check condtion
                    if (rootElement.mozRequestFullScreen) {
                        try {
                            await rootElement.mozRequestFullScreen()
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (rootElement.requestFullscreen) {
                        try {
                            await rootElement.requestFullscreen()
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (rootElement.webkitRequestFullScreen) {
                        try {
                            await rootElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (rootElement.msRequestFullscreen) {
                        try {
                            await rootElement.msRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
                        }
                        catch (error) {
                            return;
                        }
                    }

                    // Set the flag related to the full-screen and the ribbon bar
                    $scope.ribbonService.isFullScreen = true;
                    $scope.hideRibbonBar = true;

                    // Change the 'position' style of the neighborhood div tag of ribbon-bar
                    var el = document.getElementsByClassName('client-view')[0];
                    if (el)
                        el.style.position = 'absolute';
                }
                //if browser is in fullscreen mode, exit fullscreen
                else {

                    // Check condtion
                    if (document.mozCancelFullScreen) {
                        try {
                            await document.mozCancelFullScreen();
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (document.exitFullscreen) {
                        try {
                            await document.exitFullscreen();
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (document.webkitExitFullscreen) {
                        try {
                            await document.webkitExitFullscreen();
                        }
                        catch (error) {
                            return;
                        }
                    }
                    else if (document.msExitFullscreen) {
                        try {
                            await document.msExitFullscreen();
                        }
                        catch (error) {
                            return;
                        }
                    }

                    // Set the flag related to the full-screen and the ribbon bar
                    $scope.ribbonService.isFullScreen = false;
                    $scope.hideRibbonBar = false;

                    // Change the 'position' style of the neighborhood div tag of ribbon-bar
                    var el = document.getElementsByClassName('client-view')[0];
                    if (el)
                        el.style.position = 'relative';

                    // Check if the Keyboard Lock API is supported
                    if ('keyboard' in navigator && 'lock' in navigator.keyboard) {
                        // Unlock the keyboard
                        navigator.keyboard.unlock();
                    }
                }
            };

            /**
             * Check whether the window is in the full-screen mode
             */
            $scope.isFullScreen = function isFullScreen() {
                return (document.fullscreenElement && document.fullscreenElement !== null) ||
                    (document.webkitFullscreenElement && document.webkitFullscreenElement !== null) ||
                    (document.mozFullScreenElement && document.mozFullScreenElement !== null) ||
                    (document.msFullscreenElement && document.msFullscreenElement !== null);
            }

            /**
             * Add the handler of the exit event from the full-screen mode when clicking
             * the black close button or pressing Esc key
             */
            if (document.addEventListener) {
                document.addEventListener('fullscreenchange', exitFullScreenHandler, false);
                document.addEventListener('mozfullscreenchange', exitFullScreenHandler, false);
                document.addEventListener('MSFullscreenChange', exitFullScreenHandler, false);
                document.addEventListener('webkitfullscreenchange', exitFullScreenHandler, false);
            }

            /**
             * Handler of the exit event from the full-screen mode when clicking
             * the black close button or pressing Esc key
             */
            function exitFullScreenHandler() {
                if (document.webkitIsFullScreen === false || document.mozFullScreen === false ||
                    document.msFullscreenElement === false) {

                    // Change the 'position' style of the neighborhood div tag of ribbon-bar
                    var el = document.getElementsByClassName('client-view')[0];
                    if (el) {
                        el.style.position = 'relative';
                    }

                }
            }

            /**
             * Listener to catch F11 key event
             *
             * When pressing F11, toggle in FullScreen to show/hide the ribbon bar.
             */
            document.addEventListener("keydown", function(e) {
                var F11_KEY = "F11";

                if (e.code == F11_KEY && !$rootScope.isKioskMode) {
                    e.preventDefault();
                    $scope.toggleFullScreen();
                }
            });

            /**
             * Shows/Hides Session Manager dialog
             *
             * This is a hack: get client scope and change inputMethod variable. This is the variable
             * that is changed when user selects input method in the side menu.
             */
            $scope.toggleOSK = function toggleOSK() {
                var clientScope = angular.element(document.getElementsByTagName("guac-viewport")[0]).scope();

                if (clientScope.menu.inputMethod === "none")
                    clientScope.menu.inputMethod = "osk";
                else
                    clientScope.menu.inputMethod = "none";
            }

            /**
             * Mute / Unmute Sound
             */
            $scope.ribbonService.soundMute = false;

            // When clicking the sound icon, switch sound as unmute or mute state
            $scope.switchSoundOnOff = function switchSoundOnOff() {
                $scope.ribbonService.soundMute = !$scope.ribbonService.soundMute;

                if (!!$scope.client && !!$scope.client.client) {
                    var audioPlayers = $scope.client.client.getAudioPlayers();
                    for (var index in audioPlayers) {
                        var audioPlayer = audioPlayers[index];
                        if (audioPlayer)
                            audioPlayer.setMute($scope.ribbonService.soundMute);
                    }
                }
            }

            $scope.showRibbonBar = function showRibbonBar(event) {
                event.preventDefault();
                $scope.hideRibbonBar = false;
                var ribbonBar = document.querySelector("ribbon-bar");
                if (!ribbonBar) return;
                var focusableContent = ribbonBar.querySelectorAll(".ribbon-button:not(.ng-hide)");
                if (focusableContent[0]) {
                    $scope.targetElement = focusableContent[0];
                    $scope.targetElement.focus();
                }
                $scope.ribbonService.ribbonActive = true;
                event.currentTarget.removeAttribute('tabindex');
            }

            $scope.hideRibbonMenu = function hideRibbonMenu() {
                $scope.hideRibbonBar = true;
                $scope.isSmallRibbonBarVisible = false;
                $scope.ribbonService.ribbonActive=false;
                var showRibbonButton = document.getElementById("compressed_ribbon_bar_block");
                showRibbonButton.setAttribute('tabindex', '0');
                window.focus();
            }

            // Toggle side menu
            $scope.toggleSideMenu = function toggleSideMenu() {
                $scope.ribbonService.sideMenu = !$scope.ribbonService.sideMenu;
            }

            /**
             * Toggle the settings popup menu
             */
            $scope.toggleMorePopup = function toggleMorePopup() {
                // Determine the position of popup menu
                var moreButton = document.getElementById("ribbonMore");
                var morePopup = document.getElementById("ribbonMorePopup");

                // morePopup.style.left = moreButton.offsetLeft + 'px';
                morePopup.style.left = moreButton.getBoundingClientRect().left + 73 + 'px';
                morePopup.style.top = moreButton.offsetTop + moreButton.offsetHeight + 'px';

                // Toggle the popup menu
                $scope.ribbonService.morePopupVisible = !$scope.ribbonService.morePopupVisible;
                //hide dialog except morePopup
                if (!$scope.ribbonService.introRunning)
                    hideDialogExcept("morePopupVisible");
            }


            $scope.toggleSettingsPopup = function toggleSettingsPopup() {
                // Determine the position of popup menu
                var gearButton = document.getElementById("ribbonSettingBtn");
                var settingPopup = document.getElementById("ribbonSettingsPopup");

                settingPopup.style.left = gearButton.getBoundingClientRect().left + 40 + 'px';
                settingPopup.style.top = gearButton.offsetTop + gearButton.offsetHeight + 'px';

                // Toggle the popup menu
                $scope.ribbonService.settingPopupVisible = !$scope.ribbonService.settingPopupVisible;
                // hide dialog except settingPopup
                hideDialogExcept("settingPopupVisible");
            }

            /**
             * Toggle the remote apps popup menu
             */
            $scope.toggleRemoteAppsPopup = function toggleRemoteAppsPopup() {
                // Determine the position of popup menu
                var remoteAppsButton = document.getElementById("ribbonRemoteAppsBtn");
                var remoteAppsPopup = document.getElementById("ribbonRemoteAppsPopup");

                remoteAppsPopup.style.left = remoteAppsButton.getBoundingClientRect().left + 40 + 'px';
                remoteAppsPopup.style.top = remoteAppsButton.offsetTop + remoteAppsButton.offsetHeight + 'px';

                // Toggle the popup menu
                $scope.ribbonService.remoteAppsPopupVisible = !$scope.ribbonService.remoteAppsPopupVisible;
                // hide dialog except settingPopup
                hideDialogExcept("remoteAppsPopupVisible");
            }

            /**
             * Open Multi monitor
             */
            $scope.openMultiMonitor = function openMultiMonitor() {
                $scope.$broadcast('$loadMultiMonitor');
                $scope.$broadcast('$openedMultiMonitor');

                // If this is primary screen, and openning additional monitor is requested, start MMonitor notifications
                if ($scope.ribbonService.isPrimaryScreen) {
                    if ($scope.mmonitorEventSource == null) {
                        $timeout(function() {
                            if ($scope.ribbonService.isOpenSecondMonitor)
                                startMMonitorNotification();
                        }, 5000);
                    }
                }
            }

            $scope.$on('$closedMultiMonitor', endMMonitorNotification);

            /**
             * Backup VM
             * */
             $scope.backupVM = function backupVM() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id),
                };

                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + datasource + "/vm/backup",
                    params: httpParameters
                };

                $http(req)
                .then(function (response) {
                    console.log("VM backup template");
                    console.log(response.data);
                })
                .catch(function (response) {
                    console.error("Error vm reboot: ", response.message);
                })
                .finally(function () {
                    // Empty process
                });
            }

            /**
             * Reboot VM
             */
            $scope.rebootVM = function rebootVM() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Reboot VM',
                    button_name: 'btn_ribbon_reboot_vm',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                // Action for closing notification dialog
                var OK_ACTION = {
                    name      : "APP.ACTION_ACKNOWLEDGE",
                    callback  : function cancelCallback() {
                        guacNotification.showStatus(false);
                    }
                };

                // Build array of available actions
                var actions = [OK_ACTION];

                // Show the notification dialog
                guacNotification.showStatus({
                    title   : "CLIENT.MESSAGE_TITLE",
                    text    : {
                        key : "CLIENT.TEXT_REBOOT_CLOUD_INSTANCE"
                    },
                    actions : actions
                });

                // Get the parameters
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id),
                };

                // Create the request.
                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + datasource + "/reboot",
                    params: httpParameters
                };

                // Send the http request.
                $http(req)
                .then(function (response) {
                    if (response.data.success == true) {
                        // Set the flag for restarting the windows session
                        $scope.client.clientState.willRestartWindowsVM = true;
                    }
                    else {
                        // Close the previous notification dialog
                        guacNotification.showStatus(false);

                        // Action for closing notification dialog
                        var OK_ACTION = {
                            name      : "APP.ACTION_ACKNOWLEDGE",
                            callback  : function cancelCallback() {
                                guacNotification.showStatus(false);
                            }
                        };

                        // Build array of available actions
                        var actions = [OK_ACTION];

                        // Show the notification dialog
                        guacNotification.showStatus({
                            title   : "CLIENT.MESSAGE_TITLE",
                            text    : {
                                key : "CLIENT.TEXT_REBOOT_CLOUD_INSTANCE_ERROR"
                            },
                            actions : actions
                        });
                    }
                })
                .catch(function (response) {
                    console.error("Error vm reboot: ", response.message);

                    // Close the notification dialog
                    guacNotification.showStatus(false);

                    // Show the error dialog
                    guacNotification.showStatus({
                        title: "CLIENT.MESSAGE_TITLE",
                        text: {
                            key: "CLIENT.TEXT_REBOOT_CLOUD_INSTANCE_ERROR"
                        },
                        actions: actions
                    });
                })
                .finally(function () {
                    // Empty process
                });
            }

            /**
             * Ask if the user will reboot the various kinds of VMs
             */
            $scope.queryRebootVM = function queryRebootVM(isPersistentVM) {

                // "OK" and "Cancel" buttons for confirmation dialog
                var OK_BTN = {
                    name      : "APP.ACTION_ACKNOWLEDGE",
                    callback  : function cancelCallback() {
                        guacNotification.showStatus(false);
                        /**
                         * enable-reboot parameter is used for classifying Persistent VM or not
                         * isPersistentVM set to true when enable-reboot parameter set to true without considerting AppOS parameter
                         */
                        if (isPersistentVM) {
                            $scope.rebootVM(); // to restart Persistent VM
                        }
                        else {
                            $scope.restartNonWindowSession(); // to restart Linux or Mac VM
                        }
                    },
                    className : 'cancelBtnClass'
                };

                var CANCEL_BTN = {
                    name        : "DIALOGS.BUTTON_CANCEL",
                    // Handle action
                    callback    : function acknowledgeCallback() {
                        guacNotification.showStatus(false);
                    },
                    className   : 'cancelBtnClass'
                };

                // Build array of available actions
                var actions = [OK_BTN, CANCEL_BTN];

                // Show the confirmation dialog
                guacNotification.showStatus({
                    title   : "CLIENT.MESSAGE_TITLE",
                    text    : {
                        key : "CLIENT.TEXT_QUERY_REBOOT_VM"
                    },
                    actions : actions
                });
            }

            $scope.ribbonService.queryRebootVMInSetting = function queryRebootVMInSetting(isPersistentVM){
                $scope.queryRebootVM(isPersistentVM);
            }

            /**
             * Restart MacOS / Linux Session
             */
            $scope.restartNonWindowSession = function restartNonWindowSession() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Restart session',
                    button_name: 'btn_ribbon_restart',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                // Action for closing notification dialog
                var OK_ACTION = {
                    name      : "APP.ACTION_ACKNOWLEDGE",
                    callback  : function cancelCallback() {
                        guacNotification.showStatus(false);
                    }
                };

                // Build array of available actions
                var actions = [OK_ACTION];

                // Create the text related to the kind of non-windows os.
                var successLog;
                var failLog;
                var messageText;
                var notifyText;
                if ($scope.ribbonService.licenses.hasMacOSLicence) {
                    successLog = "Restart Mac OS Session";
                    failLog = "Error Mac OS restart: ";
                    notifyText = "CLIENT.TEXT_REBOOT_MACOS_NOTIFY";
                    messageText = "CLIENT.TEXT_REBOOT_MACOS_ERROR";
                }
                else {
                    successLog = "Restart Linux Session";
                    failLog = "Error Linux restart: ";
                    notifyText = "CLIENT.TEXT_REBOOT_LINUX_NOTIFY";
                    messageText = "CLIENT.TEXT_REBOOT_LINUX_ERROR";
                }

                // Show the notification dialog
                guacNotification.showStatus({
                    title   : "CLIENT.MESSAGE_TITLE",
                    text    : {
                        key : notifyText
                    },
                    actions : actions
                });

                // Set the flag for restarting the mac/linux session
                $scope.client.clientState.willRestartNonWindowsVM = true;

                // Get the parameters
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id),
                };

                // Create the request.
                var req = {
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/vm-session/terminate",
                    params: httpParameters
                };

                // Send the http request.
                $http(req)
                .then(function (response) {
                    console.log(successLog);
                    console.log(response);

                    if (response.status !== 200) {
                        // Close the notification dialog
                        guacNotification.showStatus(false);

                        // Show the error dialog
                        guacNotification.showStatus({
                            title: "CLIENT.MESSAGE_TITLE",
                            text: {
                                key: messageText
                            },
                            actions: actions
                        });
                    }
                })
                .catch(function (response) {
                    console.error(failLog, response.status);
                    console.log(response);

                    // Close the notification dialog
                    guacNotification.showStatus(false);

                    // Show the error dialog
                    guacNotification.showStatus({
                        title: "CLIENT.MESSAGE_TITLE",
                        text: {
                            key: messageText
                        },
                        actions: actions
                    });
                })
                .finally(function() {
                    // Empty process
                });
            }

            $rootScope.goLoginPage = function() {
                var homePage = "";

                // Redirect to the home page
                if ($scope.ribbonService.licenses.subdomain) {
                    if (!$scope.ribbonService.licenses.payloadVersion) {
                        if (!$scope.ribbonService.licenses.subdomain.includes(".apporto.com")) {
                            homePage = $scope.ribbonService.licenses.subdomain  + ".apporto.com/user/logout";
                        } else {
                            homePage = $scope.ribbonService.licenses.subdomain + "/user/logout" ;
                        }
                    } else {
                        if (!$scope.ribbonService.licenses.subdomain.includes(".apporto.com")) {
                            homePage = $scope.ribbonService.licenses.subdomain  + ".apporto.com/api/auth/logout";
                        } else {
                            homePage = $scope.ribbonService.licenses.subdomain + "/api/auth/logout" ;
                        }
                    }
                } else {
                    homePage = "apporto.com";
                }

                $window.location.href = 'https://' + homePage; // Adjust the URL based on your routing
            };

            $scope.toggleChattingDialog = function toggleChattingDialog() {
                if ($scope.ribbonService.chattingVisible) {
                    $scope.ribbonService.chattingMinimized = !$scope.ribbonService.chattingMinimized;

                    if (!$scope.ribbonService.chattingMinimized) {

                        // Google Tag Manager
                        dataLayer.push({
                            event: 'Messenger icon',
                            button_name: 'btn_ribbon_messenger_icon',
                            sub_domain: $scope.ribbonService.licenses.subdomain
                        });

                        for (var j = 0; j < $scope.ribbonService.groups.length; j++) {
                            var group = $scope.ribbonService.groups[j];
                            for (var i = 0; i < $scope.ribbonService.groupMembers[group].length; i++) {
                                var index = $scope.ribbonService.activeMembers.findIndex(function (member) {
                                    return member.windows_name == $scope.ribbonService.groupMembers[group][i].windows_name;
                                })

                                if (index == -1 && $scope.ribbonService.groupMembers[group][i].isUnread) {
                                    $scope.ribbonService.groupMembers[group][i].isUnread = false;
                                    $scope.ribbonService.groupMembers[group][i].unreadMezCount = 0
                                    $scope.ribbonService.activeMembers.push($scope.ribbonService.groupMembers[group][i]);
                                }
                            }
                        }
                    }
                }
                else {
                    register();

                    $scope.ribbonService.chattingVisible = true;
                    $scope.ribbonService.chattingMinimized = false;
                }
            }

            $scope.expandChatting = function expandChatting() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Messenger contacts',
                    button_name: 'btn_ribbon_messenger_contacts',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.chattingMinimized = false;
                for (var j = 0; j < $scope.ribbonService.groups.length; j++) {
                    var group = $scope.ribbonService.groups[j];
                    for (var i = 0; i < $scope.ribbonService.groupMembers[group].length; i++) {
                        var index = $scope.ribbonService.activeMembers.findIndex(function (member) {
                            return member.windows_name == $scope.ribbonService.groupMembers[group][i].windows_name;
                        })

                        if (index == -1 && $scope.ribbonService.groupMembers[group][i].isUnread) {
                            $scope.ribbonService.groupMembers[group][i].isUnread = false;
                            $scope.ribbonService.groupMembers[group][i].unreadMezCount = 0
                            $scope.ribbonService.activeMembers.push($scope.ribbonService.groupMembers[group][i]);
                        }
                    }
                }
            }

            $scope.expandThumbnail = function expandThumbnail() {
                $scope.ribbonService.presenterThumbnailVisible = true;
                $scope.ribbonService.isPresenter = true;
                $rootScope.isPresenterEnabled = true;
                $scope.ribbonService.presenterMinimized = false;

                setTimeout(function () {
                    $('#view_el').attr('src', $rootScope.presenter_url);
                }, 0);
            }

            $scope.roomStyle = function roomStyle($index) {
                var right = ($scope.ribbonService.chattingMinimized ? 110 : 380) + 380 * $index;
                return {
                    right: right
                }
            }

            $scope.presenterStyle = function presenterStyle() {
                var right;
                if ($scope.ribbonService.licenses.hasChattingLicence && $scope.ribbonService.chattingVisible) {
                    right = ($scope.ribbonService.chattingMinimized ? 110 : 255) + 245 * $scope.ribbonService.activeMembers.length;
                }
                else {
                    right = 10;
                }
                return {
                    right: right
                }
            }

            /**
             * Function that retrieves messenger server host name information from server
             */
            function getMessengerHostname() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                    name: 'messenger-server'
                };

                return $http({
                    method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/property',
                    params: httpParameters
                })
                .then(function (response) {
                    $scope.ribbonService.kurentoHostName = response.data["messenger-server"] || "";

                    if (!$scope.ribbonService.kurentoHostName) {
                        $scope.ribbonService.licenses.hasChattingLicence = false;
                        $scope.ribbonService.chattingVisible = false;
                        $scope.ribbonService.groups = [];
                        console.log("The kurentoHostName is undefined which makes chat button disable");
                    }
                })
                .catch(function (response) {
                    console.error("Error getting messenger hostname info.");
                    $scope.ribbonService.licenses.hasChattingLicence = false;
                    $scope.ribbonService.chattingVisible = false;
                    $scope.ribbonService.groups = [];
                });
            }

            /**
             * Fetch userlist
             */
            var getUserList = function getUserList(selectedGroup, selectedUser) {

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                };

                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + authenticationService.getDataSource() + "/messenger/getUserList",
                    params: httpParameters
                };

                return $http(req)
                    .then(function (response) {
                        var data = response.data;
                        var groups = [];
                        $scope.ribbonService.userlist = data;

                        $translate('MESSENGER.FACULTY').then(function setTitle(text) {
                            for (var group in data) {
                                var index = data[group].findIndex(function (user) {
                                    return user['email'] == $scope.ribbonService.userinfo['email'];
                                });

                                if (index > -1) {
                                    data[group].splice(index, 1);
                                }

                                if (data[group].length == 0) {
                                    continue;
                                }

                                groups.push(group);

                                for (var i = 0; i < data[group].length; i++) {
                                    var fullName = userInfoService.getFullName(data[group][i]['first name'], data[group][i]['last name']);
                                    fullName = fullName ? fullName : data[group][i]['email'];
                                    var initials = userInfoService.getInitials(fullName);
                                    initials = initials ? initials : userInfoService.getInitials(data[group][i]['email']);
                                    $scope.ribbonService.userlist[group][i]['name'] = fullName;
                                    $scope.ribbonService.userlist[group][i]['initials'] = initials;
                                    $scope.ribbonService.userlist[group][i]['windows_name'] = data[group][i]['email'];
                                    $scope.ribbonService.userlist[group][i].sha256_username = sha256(data[group][i]['windows_name']);
                                    $scope.ribbonService.userlist[group][i].state = '3';

                                    if ($scope.ribbonService.userlist[group][i].Role == 'Faculty Admin') {
                                        $scope.ribbonService.userlist[group][i].name += " (" + text + ")";
                                    }

                                    var index = $scope.ribbonService.apportoMembers.findIndex(function (member) {
                                        return member['windows_name'] == $scope.ribbonService.userlist[group][i]['windows_name'];
                                    });

                                    if (index == -1) {
                                        $scope.ribbonService.apportoMembers.push($scope.ribbonService.userlist[group][i]);
                                    }
                                }
                            }

                            if (!$scope.ribbonService.userlist || $scope.ribbonService.userlist.length == 0) {
                                $scope.ribbonService.licenses.hasChattingLicence = false;
                                $scope.ribbonService.chattingVisible = false;
                                $scope.ribbonService.groups = [];
                                console.log('Either the userlist is undefined or userlist length is zero in getUserList API, which makes chat button disable');
                            }
                            else {
                                $scope.ribbonService.groups = groups;
                                $scope.ribbonService.groups.sort();
                                $scope.ribbonService.licenses.hasChattingLicence = true;
                                $scope.ribbonService.chattingVisible = true;
                                $scope.ribbonService.chattingMinimized = true;

                                if (selectedGroup && selectedUser) {
                                    if (groups.indexOf(selectedGroup) > -1) {
                                        var index = $scope.ribbonService.userlist[selectedGroup].findIndex(function (user) {
                                            return user.windows_name == selectedUser;
                                        })

                                        if (index > -1) {
                                            selected = $scope.ribbonService.userlist[selectedGroup][index];
                                            $scope.ribbonService.userlist = [];
                                            $scope.ribbonService.userlist[selectedGroup] = [];
                                            $scope.ribbonService.userlist[selectedGroup].push(selected);
                                            $scope.ribbonService.groups = [selectedGroup];
                                        }
                                        else {
                                            $scope.ribbonService.licenses.hasChattingLicence = false;
                                            $scope.ribbonService.chattingVisible = false;
                                            $scope.ribbonService.groups = [];
                                            console.log("User's windows_name is not avaliable in selected user which makes the chat button disbale")
                                        }
                                    }
                                    else {
                                        $scope.ribbonService.licenses.hasChattingLicence = false;
                                        $scope.ribbonService.chattingVisible = false;
                                        $scope.ribbonService.userlist = [];
                                        $scope.ribbonService.groups = [];
                                        console.log("Selected group is not avalible in groups which makes the chat button disable");
                                    }
                                }

                                if (selectedGroup) {
                                    $scope.ribbonService.groups = [selectedGroup];
                                }
                            }
                        });
                    }).catch(function (response) {
                        console.error("Chatting error group fetch: ", response);
                        $scope.ribbonService.userlist = [];
                        $scope.ribbonService.licenses.hasChattingLicence = false;
                    }).finally(function () { })
            }

            var register = function register() {
                if (!$scope.ribbonService.kurentoHostName) {
                    console.log("The kurentoHostName is undefined which makes chat button disable");
                    $scope.ribbonService.licenses.hasChattingLicence = false;
                    $scope.ribbonService.chattingVisible = false;
                    $scope.ribbonService.groups = [];
                    return;
                }
                $scope.ribbonService.userName = $scope.ribbonService.userinfo.windows_username;

                var wsUri = 'wss://' + $scope.ribbonService.kurentoHostName + ':8443/room';
                var wsRoomUri = 'wss://' + $scope.ribbonService.kurentoHostName + ':8000/call/websocket';

                ServiceCall.setWebSocket(wsRoomUri, {
                    user: $scope.ribbonService.userinfo.windows_username,
                    adu: $scope.ribbonService.userinfo.adu,
                    session: $routeParams.q
                });

                var roomName = $scope.ribbonService.GLOBAL_ROOM_NAME;
                if (!ServiceRoom.getKurento(roomName)) {
                    var kurento = KurentoRoom(wsUri, roomName, function (error, kurento) {

                        if (error) {
                            Rollbar.error("WebSocket connection failed with Kurento chat room" + error);
                            return console.log(error);
                        }

                        var room = kurento.Room({
                            room: kurento.roomName,
                            user: $scope.ribbonService.userName,
                            adu: $scope.ribbonService.userinfo.adu,
                            session: $routeParams.q,
                            uuid: $scope.ribbonService.uuid,
                            updateSpeakerInterval: $scope.ribbonService.updateSpeakerInterval,
                            thresholdSpeaker: $scope.ribbonService.thresholdSpeaker
                        });

                        if (!room) {
                            kurento.close(true);
                            $scope.ribbonService.roomState[kurento.roomName] = false;
                            $scope.ribbonService.userinfo.state = '3';
                            $scope.ribbonService.activeMembers = [];
                            return;
                        }

                        room.addEventListener("room-connected", function (roomEvent) {
                            $scope.ribbonService.roomState[kurento.roomName] = true;
                            $scope.ribbonService.userinfo.state = '0';
                            var participants = roomEvent.participants;
                            var offline = roomEvent.offline;

                            ServiceParticipant.participants = [];

                            for (var i = 0; i < participants.length; i++) {
                                ServiceParticipant.addParticipant(participants[i]);
                            }

                            if (offline.length > 0) {
                                for (var i = 0; i < offline.length; i++) {
                                    ServiceParticipant.showMessage(offline[i].msgid, offline[i].msgroom, offline[i].msguser,
                                        offline[i].msgmessage, offline[i].msgdest, offline[i].msgtime, offline[i].msgtype);
                                }
                            }

                            setTimeout(function () {
                                var messages = ServiceParticipant.messages;
                                var historyMembers = [];
                                if (!messages) messages = [];

                                for (var groupName in $scope.ribbonService.groupMembers) {
                                    for (var i = 0; i < $scope.ribbonService.groupMembers[groupName].length; i++) {
                                        var memberName = $scope.ribbonService.groupMembers[groupName][i]['windows_name'];
                                        var index = historyMembers.findIndex(function (member) {
                                            return member == memberName;
                                        });

                                        if (index > -1) continue;
                                        historyMembers.push(memberName);

                                        if (!messages[memberName]) {
                                            messages[memberName] = [];
                                        }
                                        if (!messages[memberName][$scope.ribbonService.GLOBAL_ROOM_NAME]) {
                                            messages[memberName][$scope.ribbonService.GLOBAL_ROOM_NAME] = [];
                                        }

                                        index = $scope.ribbonService.apportoMembers.findIndex(function (member) {
                                            return member['windows_name'] == memberName;
                                        });

                                        if (index < 0) continue;
                                        $scope.ribbonService.apportoMembers[index]['isGetHistory'] = true;

                                        var time;
                                        if (messages[memberName][$scope.ribbonService.GLOBAL_ROOM_NAME].length == 0) {
                                            time = new Date().getTime();
                                        }
                                        else {
                                            time = messages[memberName][$scope.ribbonService.GLOBAL_ROOM_NAME][0]['timestamp'];
                                        }

                                        if (messages[memberName][$scope.ribbonService.GLOBAL_ROOM_NAME].length < 50) {
                                            kurento.getHistory($scope.ribbonService.GLOBAL_ROOM_NAME,
                                                $scope.ribbonService.userinfo['windows_username'], memberName, 50, time, '');
                                        }
                                    }
                                }

                                for (var j = 0; j < $scope.ribbonService.groups.length; j++) {
                                    var group = $scope.ribbonService.groups[j];
                                    if ($scope.ribbonService.groupMembers[group] && !!$scope.ribbonService.groupMembers[group].length) {
                                        for (i = 0; i < $scope.ribbonService.groupMembers[group].length; i++) {
                                            index = $scope.ribbonService.activeMembers.findIndex(function (member) {
                                                return member.windows_name == $scope.ribbonService.groupMembers[group][i].windows_name;
                                            })

                                            if (index == -1 && $scope.ribbonService.groupMembers[group][i].isUnread) {
                                                $scope.ribbonService.groupMembers[group][i].isUnread = false;
                                                $scope.ribbonService.groupMembers[group][i].unreadMezCount = 0
                                                $scope.ribbonService.activeMembers.push($scope.ribbonService.groupMembers[group][i]);
                                            }
                                        }
                                    }
                                }
                            }, 1000);
                        });

                        room.addEventListener("participant-joined", function (participantEvent) {
                            participantEvent.participant.setState('available');
                            ServiceParticipant.addParticipant(participantEvent.participant);
                        });

                        room.addEventListener("participant-left", function (participantEvent) {
                            ServiceParticipant.removeParticipant(participantEvent.participant);
                        });

                        room.addEventListener("participant-state-changed", function (msg) {
                            ServiceParticipant.updateParticipant(msg.user, msg.state);
                        });

                        room.addEventListener("newMessage", function (msg) {
                            if (!$scope.ribbonService.userinfo.windows_username.includes(msg.user)) {
                                if (msg.type == $scope.ribbonService.SHARE_URL) {
                                    $rootScope.$broadcast('share_url', msg);
                                }
                                else {
                                    ServiceParticipant.showMessage(msg.id, msg.room, msg.user, msg.message, msg.dest, msg.time, msg.type);
                                }
                            }
                            else {
                                ServiceParticipant.showSendMessage(msg.room, msg.dest, msg.message, msg.user, msg.time, msg.type);
                            }
                        });

                        room.addEventListener("synRecvMessage", function (msg) {
                            ServiceParticipant.sycRevMessage($scope.ribbonService.userinfo.windows_username, msg.id);
                        });

                        room.addEventListener("get-history", function (historyEvent) {
                            ServiceParticipant.showHistory(historyEvent);
                            var index = $scope.ribbonService.apportoMembers.findIndex(function (member) {
                                return member['windows_name'] == historyEvent.user;
                            });

                            if (index > -1)
                                $scope.ribbonService.apportoMembers[index]['isGetHistory'] = false;
                        });

                        room.addEventListener("error-room", function (error) {
                            Rollbar.error("WebSocket error-room with Kurento chat room" + error);
                            console.error('error-room: ', error);
                            $scope.ribbonService.roomState[kurento.roomName] = false;
                            $scope.ribbonService.userinfo.state = '3';
                            $scope.ribbonService.activeMembers = [];
                            $scope.ribbonService.activeMembers = [];
                        });

                        room.addEventListener("error-media", function (msg) {
                            Rollbar.error("WebSocket error-media with Kurento chat room" + msg);
                            console.error('error-media: ', msg.error);
                            $scope.ribbonService.roomState[kurento.roomName] = false;
                            $scope.ribbonService.userinfo.state = '3';
                            $scope.ribbonService.activeMembers = [];
                            $scope.ribbonService.activeMembers = [];
                            kurento.close(true);
                        });

                        room.addEventListener("room-closed", function (msg) {
                            if (msg.room !== kurento.roomName) {
                                console.error("Closed room name doesn't match this room's name",
                                    msg.room, kurento.roomName);
                            }
                            else {
                                kurento.close(true);
                                console.log('Room ' + msg.room + ' has been forcibly closed from server');
                            }
                        });

                        room.addEventListener("lost-connection", function (msg) {
                            $scope.ribbonService.roomState[kurento.roomName] = false;
                            $scope.ribbonService.userinfo.state = '3';
                            $scope.ribbonService.activeMembers = [];
                            Rollbar.error("Websocket lost connection with Kurento chat room" + msg);
                            console.error("Websocket lost connection with Kurento chat room", msg);
                        });

                        room.connect();
                    });

                    //save kurento & roomName & userName in service
                    ServiceRoom.setKurento(kurento, roomName);
                }
                ServiceRoom.setUserName($scope.ribbonService.userName);
                ServiceRoom.setUserName($scope.ribbonService.userName);
            }

            $scope.$on('messages:updated', function (event, data) {
                if (!$scope.ribbonService.chattingVisible) {
                    $scope.ribbonService.chattingVisible = true;
                    setTimeout(function () {
                        $rootScope.$broadcast('messages:updated', data);
                    }, 500);
                }
            });

            $scope.getUnreadCount = function () {
                var messages = ServiceParticipant.messages;
                var unreadCount = 0;

                for (var from in messages) {
                    for (var group in messages[from]) {
                        if (!!messages[from][group]) {
                            unreadCount += messages[from][group].filter((obj) => obj.state != 'read' && obj.user == from).length;
                        }
                    }
                }

                if (unreadCount <= 50) {
                    return unreadCount;
                }
                else {
                    return '50+';
                }
            }

            var poorNetworkWarningMessage = function poorNetworkWarningMessage(bodyMsg) {
                webNotification && webNotification.showNotification("Connection lost", {
                    body: bodyMsg,
                    requireInteraction: true,
                    onClick: function onNotificationClicked() {
                        try {
                            window.focus();
                            this.cancel();
                        }
                        catch (ex) { }
                    },
                    icon: 'app/ext/ribbon/images/favicons/apple-icon-60x60.png'
                }, function onShow(error, hide) {
                    if (error) {
                        console.log('Unable to show notification: ' + error.message);
                    }
                    else {
                        console.log('Notification Shown.Warning!');
                    }
                })
            }

            $scope.ribbonService.register = register;
            $scope.ribbonService.getUserList = getUserList;
            $scope.ribbonService.expandChatting = $scope.expandChatting;


            /**
             * Get all available assignment list
             */
            $scope.getAssignmentList = function getAssignmentList() {

                $scope.ribbonService.skeletonLoad.assignmentList = true;
                $scope.ribbonService.skeletonLoad.assignmentDetail = true;
                $scope.ribbonService.assignmentManagementDialogVisible = true;
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token:      encodeURIComponent(authenticationService.getCurrentToken()),
                    id:         encodeURIComponent(clientIdentifier.id),
                    datasource: encodeURIComponent(clientIdentifier.dataSource),
                };
                var req = {
                    method: 'GET',
                    url: "api/lti/getAssignmentList",
                    params: httpParameters
                };

                $http(req)
                .then(function (response) {
                    $scope.ribbonService.courseName = response.data.course_name;
                    $scope.ribbonService.assignmentList = response.data.assignments;
                    $rootScope.selectedAssignment = $scope.ribbonService.assignmentList[0];
                    $scope.ribbonService.assignmentManagementDialogVisible = true;
                    console.log(response.data);
                })
                .catch(function (response) {
                    $translate('ASSIGNMENT.ERROR_GET_ASSIGNMENT_LIST').then(function setError(text) {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = text;
                        $scope.infoService.infoDialogVisible = true;
                    });
                    console.error("Error get assignment list: ", response.data.Message);
                })
                .finally(function () {
                    $scope.ribbonService.skeletonLoad.assignmentList = false;
                    $scope.ribbonService.skeletonLoad.assignmentDetail = false;
                });
            }
        }
    ]);
