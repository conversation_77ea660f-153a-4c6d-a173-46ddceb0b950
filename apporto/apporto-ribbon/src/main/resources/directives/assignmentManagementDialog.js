/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays assignment management screen
 */
angular.module('ribbon').directive('assignmentManagementDialog', ['$window', function assignmentManagementDirective($window) {

    return {
        restrict: 'E',
        scope: {},

        templateUrl: 'app/ext/ribbon/templates/assignment-management-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', '$routeParams', '$sce', '$timeout',function assignmentManagementDialogController($scope, $injector, $rootScope,  $routeParams, $sce, $timeout) {

            var ClientIdentifier        = $injector.get('ClientIdentifier');
            var authenticationService   = $injector.get('authenticationService');
            var $timeout                = $injector.get('$timeout');
            var $http                   = $injector.get('$http');
            $scope.ribbonService        = $injector.get('ribbonService');
            var ManagedClient           = $injector.get('ManagedClient');
            var ManagedFilesystem       = $injector.get('ManagedFilesystem');
            var guacClientManager       = $injector.get('guacClientManager');
            var $rootScope              = $injector.get('$rootScope');
            var $translate               = $injector.get('$translate');

            $scope.client = null;
            $scope.filesystem = null;

            var RDP_FS_NAME = "Shared Drive"; // Default name given by guacd to RDP FS
            var INITIAL_DIRECTORIES = ["Desktop", "Bureau"];

            var cachedAssignment = null;

            const locale = $translate.use() || 'en-US';

            function getFilesystem() {
                $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                for (var fs in $scope.client.filesystems) {
                    // Assume that first filesystem that is not having default RDP_FS_NAME is SFTP filesystem
                    if ($scope.client.filesystems[fs].name !== RDP_FS_NAME) {
                        return new ManagedFilesystem($scope.client.filesystems[fs]);
                    }
                }

                console.warn("No valid filesystem found.");
                return null;
            };
            
            $scope.getTrustedHtml = function(html) {
                return $sce.trustAsHtml(html);
            };

            $scope.search = {
                searchQuery: ''
            }

            $rootScope.resubmitEnabled = false;

            $scope.filteredAssignments = []

            var assignmentDetailsTimeout = null;

            /**
             * Close the dialog.
             */
            $scope.closeAssignmentManagementDialog = function closeAssignmentManagementDialog() {
                $scope.ribbonService.assignmentManagementDialogVisible = false;
                $scope.ribbonService.fileExplorerVisible = false;
            }   

            $rootScope.enableResubmit = function enableResubmit() {
                $rootScope.resubmitEnabled = true;
            }   

            function changeInitialDirectory(fs) {

                const existingDir = INITIAL_DIRECTORIES.find(dir => fs.root.files.hasOwnProperty(dir));
                if (existingDir) {
                    ManagedFilesystem.changeDirectory(fs, fs.root.files[existingDir]);
                } else {
                    console.error("Directory change failed, opening dialog in root directory.");
                }
                $scope.filesystem = fs;
            }

            $scope.$watch('ribbonService.assignmentManagementDialogVisible', function (newValue) {
                if (newValue) {
                    var fs = getFilesystem();
                    if (fs != null) {
                        // Check if filesystem contains mandatory directory (Desktop)
                        if (fs.root && fs.root.files) {
                            if (INITIAL_DIRECTORIES.some(dir => fs.root.files.hasOwnProperty(dir))) {
                                changeInitialDirectory(fs);
                            } else {
                                console.warn("Initial directory not found, refreshing...");
                                ManagedFilesystem.changeDirectory(fs, fs.root);
                        
                                // Wait for the root directory to load completely
                                $timeout(function () {
                                    changeInitialDirectory(fs);
                                }, 3000);
                            }
                        } else {
                            console.error("Filesystem root is undefined or files are missing.");
                        }
                    }
                }
                else {
                    $scope.ribbonService.courseName = "";
                    $scope.ribbonService.assignmentList = [];
                    $rootScope.selectedAssignment = null;
                    $scope.filteredAssignments = [];
                    $scope.filesystem = null;
                }
            });

            /**
             * Used to decide whether the assignment is locked or not
             * 
             * @param {Object} assignment an object
             * @returns {boolean} returns true if current time is lie between unlock and lock time
             */
            $scope.isAssignmentUnlocked = function isAssignmentUnlocked(assignment) {
                if (!assignment || (!assignment.unlock_at && !assignment.lock_at)) {
                    return true; // If no assignment or no unlock/lock times are provided, keep it unlocked
                }
            
                var currTime = new Date();
                var unlockTime = assignment.unlock_at ? new Date(assignment.unlock_at) : null;
                var lockTime = assignment.lock_at ? new Date(assignment.lock_at) : null;
            
                if (unlockTime && lockTime) {
                    return currTime >= unlockTime && currTime < lockTime;
                }
            
                if (unlockTime) {
                    return currTime >= unlockTime;
                }
            
                if (lockTime) {
                    return currTime < lockTime;
                }
            
                return true;
            }

            $scope.checkAssignmentStatus = function checkAssignmentStatus(assignment) {
                if (assignment) {

                    const currTime = new Date();
                    const unlockTime = assignment.unlock_at ? new Date(assignment.unlock_at) : null;
                    const lockTime = assignment.lock_at ? new Date(assignment.lock_at) : null;

                    if (assignment.has_submitted_submissions) {
                        if (lockTime && currTime >= lockTime) {
                            return $scope.ribbonService.assignmentStatus.SUBMITTED_LOCKED;
                        }
                        return $scope.ribbonService.assignmentStatus.SUBMITTED;
                    }
            
                    if (!unlockTime && !lockTime) {
                        return $scope.ribbonService.assignmentStatus.UNLOCKED;
                    }
            
                    if (unlockTime && currTime < unlockTime) {
                        return $scope.ribbonService.assignmentStatus.LOCKED_FUTURE;
                    }
            
                    if (lockTime && currTime >= lockTime) {
                        return $scope.ribbonService.assignmentStatus.LOCKED_PAST_DUE;
                    }
            
                    if (unlockTime && lockTime) {
                        return currTime >= unlockTime && currTime < lockTime 
                            ? $scope.ribbonService.assignmentStatus.UNLOCKED 
                            : $scope.ribbonService.assignmentStatus.LOCKED_PAST_DUE;
                    }
            
                    if (unlockTime) {
                        return currTime >= unlockTime 
                            ? $scope.ribbonService.assignmentStatus.UNLOCKED 
                            : $scope.ribbonService.assignmentStatus.LOCKED_FUTURE;
                    }
            
                    if (lockTime) {
                        return currTime < lockTime 
                            ? $scope.ribbonService.assignmentStatus.UNLOCKED 
                            : $scope.ribbonService.assignmentStatus.LOCKED_PAST_DUE;
                    }
            
                    return $scope.ribbonService.assignmentStatus.UNLOCKED;
                }
            
                return $scope.ribbonService.assignmentStatus.UNLOCKED;
            }

            $scope.downloadAllFiles = function downloadAllFiles(assignment) {
                
                if(cachedAssignment && cachedAssignment.id == assignment.id) {
                    $scope.downloadAssignment(cachedAssignment);
                    return;
                }

                var tempAssignment = angular.copy(assignment);
                $scope.getAssignmentDetails(assignment.id).then(function (data) {
                    if (data) {
                        if (!tempAssignment) {
                            tempAssignment = {};
                        }
        
                        // Merge new data while avoiding duplicates
                        Object.keys(data).forEach(key => {
                            if (!tempAssignment.hasOwnProperty(key)) {
                                tempAssignment[key] = data[key];
                            }
                        });
                        cachedAssignment = tempAssignment;
                        $scope.downloadAssignment(tempAssignment);
                    }
                }).catch(function (error) {
                    console.error("Error fetching assignment details:", error);
                });
            }

            $scope.downloadAssignment = async function downloadAssignment(assignment) {
                try {
                    // Validate assignment and its files
                    if (assignment && assignment.assignment_files) {
                        for (const file of assignment.assignment_files) {
                            await downloadIndAssignment(file.url);
                        }

                    } else {
                        console.error('No assignment files found');
                    }
                } catch (error) {
                    console.error('Error during assignment download or upload:', error);
                }
            };

            $rootScope.getAssignmentDetails = $scope.getAssignmentDetails;

            async function downloadIndAssignment(assignmentUrl) {
                try {
                    // Validate assignment and its files
                    if (!assignmentUrl || assignmentUrl.length === 0) return;

                    var requestBody = {
                        fileUrl: assignmentUrl
                    };
            
                    // Prepare HTTP parameters
                    const clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    const httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientIdentifier.id),
                        datasource: encodeURIComponent(clientIdentifier.dataSource)
                    };
            
                    // Make the HTTP request
                    const response = await $http({
                        method: 'POST',
                        url: "api/lti/downloadAssignment",
                        params: httpParameters,
                        data: requestBody,
                        responseType: 'arraybuffer'
                    });
            
                    // Create a Blob and File object
                    const contentDisposition = response.headers('Content-Disposition');
                    let fileName = 'downloaded_file';

                    if (contentDisposition && contentDisposition.includes('filename=')) {
                        fileName = contentDisposition.split('filename=')[1].trim().replace(/['"]/g, '');
                    }

                    var blob = new Blob([response.data], { type: response.headers('Content-Type') });
                    const file = new File([blob], fileName, { type: blob.type });

                    // Upload the file
                    await ManagedClient.uploadFile($scope.client, file, $scope.filesystem);

                } catch (error) {
                    console.error('Error during assignment download or upload:', error);
                }
            };

            $scope.downloadDescription= async function downloadDescription() {

                try {
                    // Validate HTML content
                    const descriptionHtml = $rootScope.selectedAssignment.description;
                    if (!descriptionHtml || descriptionHtml.length === 0) return;

                    var enhancedHtml = descriptionHtml;

                    if($rootScope.selectedAssignment.description){
                        enhancedHtml = "<h4>Assignment (" + $rootScope.selectedAssignment.name + ") description</h4>" + descriptionHtml;
                    }
                
                    var requestBody = {
                        description: enhancedHtml,
                        assignmentId: $rootScope.selectedAssignment.id
                    };
                    
                    // Prepare HTTP parameters
                    const clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    const httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientIdentifier.id),
                        datasource: encodeURIComponent(clientIdentifier.dataSource)
                    };
                    
                    // Make the HTTP request
                    const response = await $http({
                        method: 'POST',
                        url: "api/lti/downloadDescription",
                        params: httpParameters,
                        data: requestBody,
                        responseType: 'arraybuffer'
                    });
                    
                    // Create a Blob and File object
                    const contentDisposition = response.headers('Content-Disposition');
                    let fileName = 'description';
            
                    if (contentDisposition && contentDisposition.includes('filename=')) {
                        fileName = contentDisposition.split('filename=')[1].trim().replace(/['"]/g, '');
                    }
            
                    var blob = new Blob([response.data], { type: response.headers('Content-Type') });
                    const file = new File([blob], fileName, { type: blob.type });

                    // Upload the file
                    await ManagedClient.uploadFile($scope.client, file, $scope.filesystem);

                } catch (error) {
                    console.error('Error during HTML to text conversion or upload:', error);
                }

            }

            $scope.assignmentStatusGenerator = function assignmentStatusGenerator(assignment,status) {
                const options = { month: 'long', day: 'numeric', year: 'numeric' };
                const timeOptions = { hour: 'numeric', minute: '2-digit', hour12: true };

                status=status || ""
            
                if (status === $scope.ribbonService.assignmentStatus.SUBMITTED || status === $scope.ribbonService.assignmentStatus.SUBMITTED_LOCKED) {
                    if (assignment.submitted_at) {
                        const submittedAt = new Date(assignment.submitted_at);
                        return $translate.instant('ASSIGNMENT.SUBMITTED_INFO_DATE',{date:submittedAt.toLocaleDateString(locale, options), time: submittedAt.toLocaleTimeString(locale, timeOptions).replace(/\u00A0/g, ' ')});
                    }
                    return $translate.instant('ASSIGNMENT.SUBMITTED_INFO');
                }
            
                if (status === $scope.ribbonService.assignmentStatus.UNLOCKED) {
                    if (assignment.due_at) {
                        const dueAt = new Date(assignment.due_at);
                        return $translate.instant('ASSIGNMENT.DUE_INFO_DATE',{date:dueAt.toLocaleDateString(locale, options), time: dueAt.toLocaleTimeString(locale, timeOptions).replace(/\u00A0/g, ' ')});
                    }
                    return $translate.instant('ASSIGNMENT.DUE_INFO');
                }
            
                if (status === $scope.ribbonService.assignmentStatus.LOCKED_FUTURE) {
                    const unlockAt = new Date(assignment.unlock_at);
                    return $translate.instant('ASSIGNMENT.UNLOCK_INFO_DATE',{date:unlockAt.toLocaleDateString(locale, options), time: unlockAt.toLocaleTimeString(locale, timeOptions).replace(/\u00A0/g, ' ')});
                }
            
                if (status === $scope.ribbonService.assignmentStatus.LOCKED_PAST_DUE) {
                    const lockAt = new Date(assignment.lock_at);
                    return $translate.instant('ASSIGNMENT.LOCKED_INFO_DATE',{date:lockAt.toLocaleDateString(locale, options), time: lockAt.toLocaleTimeString(locale, timeOptions).replace(/\u00A0/g, ' ')})
                }
            
                return $translate.instant('ASSIGNMENT.NO_STATUS_INFO');
            };

            $scope.canShowResubmitButton = function(assignment) {
                if (!assignment) return false;
                // Only show for submitted assignments
                if (assignment.status !== $scope.ribbonService.assignmentStatus.SUBMITTED) return false;
                // If no due date, allow resubmit
                if (!assignment.due_at) return true;
                // Compare current time with due date
                var now = new Date();
                var due = new Date(assignment.due_at);
                return now < due;
            };

            /**
             * getting assignment details 
             */
            $scope.getAssignmentDetails = function getAssignmentDetails(assignmentId) {

                // Store the current assignment ID to check if it's still relevant when the response arrives
                var requestedAssignmentId = assignmentId;
                
                $scope.ribbonService.skeletonLoad.assignmentDetail = true;
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var httpParameters = {
                    token:      encodeURIComponent(authenticationService.getCurrentToken()),
                    id:         encodeURIComponent(clientIdentifier.id),
                    datasource: encodeURIComponent(clientIdentifier.dataSource),
                    assn_id:    assignmentId
                };
                var req = {
                    method: 'GET',
                    url: "api/lti/getAssignment",
                    params: httpParameters
                };

                return $http(req)
                .then(function (response) {
                    if (requestedAssignmentId === $rootScope.selectedAssignment.id) {
                        if (response.data) {
                            // Ensure $rootScope.selectedAssignment is initialized
                            if (!$rootScope.selectedAssignment) {
                                $rootScope.selectedAssignment = {};
                            } 
        
                            // Merge new data while avoiding duplicates
                            Object.keys(response.data).forEach(key => {
                                   $rootScope.selectedAssignment[key] = response.data[key];
                            });
                        }
                    }

                })
                .catch(function (response) {
                    $translate('ASSIGNMENT.ERROR_GET_ASSIGNMENT_LIST').then(function setError(text) {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = text;
                        $scope.infoService.infoDialogVisible = true;
                    });
                    console.error("Error get assignment list: ", response.data.Message);
                })
                .finally(function () {
                    if (requestedAssignmentId === $rootScope.selectedAssignment.id) {
                        if($rootScope.selectedAssignment.submitted_files_count > 0){
                            $rootScope.selectedAssignment.has_submitted_submissions = true;
                        }
                        else{
                            $rootScope.selectedAssignment.has_submitted_submissions = false;
                        }
                        const count = $rootScope.selectedAssignment.submitted_files_count;

                        $rootScope.selectedAssignment.submission_text = $translate.instant(
                            count > 1 ? 'ASSIGNMENT.SUBMITTED_MULTIPLE' : 'ASSIGNMENT.SUBMITTED_ONE',
                            { count: count }
                        );
                        let status = $scope.checkAssignmentStatus($rootScope.selectedAssignment);
                        $rootScope.selectedAssignment.status = status;
                        $rootScope.selectedAssignment.generated_status = $scope.assignmentStatusGenerator($rootScope.selectedAssignment, status);
                        $scope.ribbonService.assignmentList = $scope.ribbonService.assignmentList.map(function(assignment) {
                            if (assignment.id === $rootScope.selectedAssignment.id) {
                                return $rootScope.selectedAssignment;
                            }
                            return assignment;
                        });
                        $scope.filteredAssignments = $scope.filterAssignments();
                        $rootScope.selectedAssignment.has_submitted_submissions_count = $rootScope.selectedAssignment.submitted_files_count;
                        if($rootScope.selectedAssignment.allowed_attempts && $rootScope.selectedAssignment.attempt){
                            $rootScope.selectedAssignment.attemptAllowed = $rootScope.selectedAssignment.allowed_attempts > 0 ? $rootScope.selectedAssignment.allowed_attempts > $rootScope.selectedAssignment.attempt: true;
                        }else{
                            $rootScope.selectedAssignment.attemptAllowed = true;
                        }


                        if($rootScope.selectedAssignment.assignment_files_count == 1){
                            $rootScope.selectedAssignment.attachment_info = $translate.instant('ASSIGNMENT.ATTACHMENT_SINGLE');
                        }
                        else if($rootScope.selectedAssignment.assignment_files_count > 1){
                            $rootScope.selectedAssignment.attachment_info = $translate.instant('ASSIGNMENT.ATTACHMENT_MULTIPLE',{count:$rootScope.selectedAssignment.assignment_files_count});
                        }
                        else{
                            $rootScope.selectedAssignment.attachment_info = $translate.instant('ASSIGNMENT.ATTACHMENT_NONE');
                        }
                        cachedAssignment = $rootScope.selectedAssignment;
                        $scope.ribbonService.assignmentManagmentFiles = true;
                    }
                    $scope.ribbonService.skeletonLoad.assignmentDetail = false;
                });
            }

            $rootScope.getAssignmentDetails = $scope.getAssignmentDetails;

            $scope.selectAssignment = function selectAssignment(assignment) {
                $rootScope.resubmitEnabled = false;
                $rootScope.selectedAssignment = angular.copy(assignment);

                if (assignmentDetailsTimeout) {
                    $timeout.cancel(assignmentDetailsTimeout);
                }
                
                assignmentDetailsTimeout = $timeout(function() {
                    $scope.getAssignmentDetails(assignment.id);
                }, 300);
            }

            $scope.filterAssignments = function() {
                return $scope.ribbonService.assignmentList.filter(function(assignment) {
                    if (!$scope.search.searchQuery) return true;
                    const searchTerm = $scope.search.searchQuery.toLowerCase();
                    return assignment.name.toLowerCase().includes(searchTerm);
                });
            };

            $scope.handleSearchChange = function() {
                $scope.filteredAssignments = $scope.filterAssignments();
            };

            $scope.$watch('ribbonService.skeletonLoad.assignmentList', function (visible) {
                if (!visible && $scope.ribbonService.assignmentManagementDialogVisible && $scope.ribbonService.assignmentList && $scope.ribbonService.assignmentList.length > 0) {
                    // Map additional status properties to each assignment
                    $scope.ribbonService.assignmentList = $scope.ribbonService.assignmentList.map(assignment => {
                        let status = $scope.checkAssignmentStatus(assignment);
                        return angular.extend({}, assignment, {
                            status: status,
                            generated_status: $scope.assignmentStatusGenerator(assignment, status)
                        });
                    });
                    $scope.filteredAssignments = $scope.filterAssignments();
                    
                    let defaultAssignment = determineDefaultAssignment($scope.ribbonService.assignmentList);
                    $scope.selectAssignment(defaultAssignment);

                    $timeout(function() {
                        handleScrollPosition($scope.ribbonService.assignmentList);
                    }, 200);
                }
            });

            function determineDefaultAssignment(assignments) {
                // If only one assignment exists, select it
                if (assignments.length === 1) {
                    return assignments[0];
                }
                
                const currentDate = new Date();
                
                // Check if assignments have due dates
                const hasDueDates = assignments.some(a => a.due_at);
                
                if (hasDueDates) {
                    //Select first assignment with due date >= current date
                    const upcomingAssignments = assignments.filter(a => 
                        a.due_at && new Date(a.due_at) >= currentDate
                    ).sort((a, b) => new Date(a.due_at) - new Date(b.due_at));
                    
                    if (upcomingAssignments.length > 0) {
                        return upcomingAssignments[0];
                    }
                    
                    // If no upcoming assignments, return the most recent one
                    return assignments.filter(a => a.due_at)
                        .sort((a, b) => new Date(b.due_at) - new Date(a.due_at))[0];
                } else {
                    // Select first unsubmitted assignment
                    const unsubmittedAssignments = assignments.filter(a => 
                        !a.has_submitted_submissions
                    );
                    
                    if (unsubmittedAssignments.length > 0) {
                        return unsubmittedAssignments[0];
                    }
                    
                    // If all are submitted, select the first one
                    return assignments[0];
                }
            }

            function handleScrollPosition(assignments) {
                // Calculate how many assignments can fit in the visible area
                const cardHeight = 80;
                const cardGap = 20;
                const totalCardHeight = cardHeight + cardGap;
                
                // Viewport height is calc(70vh - 60px) from CSS
                const viewportHeight = Math.floor(window.innerHeight * 0.7) - 60;
                const visibleAssignmentsCount = Math.floor(viewportHeight / totalCardHeight);
                
                // If there are more assignments than can fit, handle scrolling
                if (assignments.length > visibleAssignmentsCount) {
                    const currentDate = new Date();
                    const hasDueDates = assignments.some(a => a.due_at);
                    
                    if (hasDueDates) {
                        //Scroll to show the last assignment with due date < current date
                        const pastAssignments = assignments.filter(a => 
                            a.due_at && new Date(a.due_at) < currentDate
                        ).sort((a, b) => new Date(a.due_at) - new Date(b.due_at));
                        
                        if (pastAssignments.length > 0) {
                            const lastPastAssignment = pastAssignments[pastAssignments.length - 1];
                            scrollToAssignment(lastPastAssignment, assignments);
                        }
                    } else {
                        //Scroll to show the last submitted assignment
                        const submittedAssignments = assignments.filter(a => 
                            a.has_submitted_submissions
                        );
                        
                        if (submittedAssignments.length > 0) {
                            const lastSubmittedAssignment = submittedAssignments[submittedAssignments.length - 1];
                            scrollToAssignment(lastSubmittedAssignment, assignments);
                        }
                    }
                }
            }
            
            // Function to scroll to a specific assignment
            function scrollToAssignment(assignment, allAssignments) {
                const assignmentList = document.querySelector('.assignment-list');
                if (!assignmentList) return;
                
                // Find the index of the assignment in the list
                const index = allAssignments.findIndex(a => a.id === assignment.id);
                if (index === -1) return;
                
                // Calculate the scroll position
            
                const cardHeight = 80;
                const cardGap = 20;
                const totalCardHeight = cardHeight + cardGap;
                
                // Add extra height for the search box if present
                const searchBoxHeight = document.querySelector('.search-container') ? 
                    document.querySelector('.search-container').offsetHeight : 0;
                
                // Scroll to position
                const scrollTop = (index * totalCardHeight) + searchBoxHeight;
                assignmentList.scrollTop = scrollTop;
            }

        }] // end assignment management dialog controller
    };
}]);

