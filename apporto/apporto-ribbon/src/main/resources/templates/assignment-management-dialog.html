<div class="dialog-middle">
    <div class="assignment-management-dialog dialog" id="dlg-assignment-manage">
      <button class="close-dialog assignment-manage" ng-click="closeAssignmentManagementDialog()" tabindex="0" aria-label="Close"></button>
      <div ng-class="{'submit-loading' : $root.assignmentSubmitLoader}" style=" 
        margin: 0;
        padding: 1em 1em;
      ">
        <div class="head" ng-if="!ribbonService.skeletonLoad.assignmentList">
            <p class="title">{{ 'ASSIGNMENT.COURSE_ASSIGNMENTS' | translate:{COURSE_NAME: ribbonService.courseName} }}</p>
            <p class="assignment-info">{{ 'ASSIGNMENT.SELECT_ASSIGNMENT' | translate }}</p>
        </div>
        <div class="head skeleton-load-item" ng-if="ribbonService.skeletonLoad.assignmentList">
            <div class="title"></div>
            <div class="assignment-info"></div>
        </div>

        <div class="assignment-management-internal">
          <div class="no-assignment-content" ng-if="!ribbonService.skeletonLoad.assignmentList && (!ribbonService.assignmentList || ribbonService.assignmentList.length === 0)">
            <p class="no-item-description">There are no available assignments for the course.</p>
          </div>

          <div class="assignment-list skeleton-container" ng-if="ribbonService.skeletonLoad.assignmentList">
            <div class="assignment-list-card" ng-repeat="n in [].constructor(5) track by $index">
              <div class="card-head">
                <div class="card-title"></div>
                <div class="card-disp"></div>
              </div>
            </div>
          </div>

          <div class="assignment-list" ng-if="!ribbonService.skeletonLoad.assignmentList && ribbonService.assignmentList && ribbonService.assignmentList.length > 0">
            <div class="search-container">
              <input type="text" ng-model="search.searchQuery" ng-change="handleSearchChange()" placeholder="Search assignments..." class="assignment-search-input">
            </div>
            <div class="assignment-list-card" ng-repeat="assignment in filteredAssignments" ng-click="selectAssignment(assignment)" ng-class="{'selected-card' : $root.selectedAssignment.id == assignment.id}">
              <div class="card-head">
                <p class="card-title">{{ assignment.name }}</p>
                <div class="submitted-assignment-icon" ng-if="assignment.status === ribbonService.assignmentStatus.SUBMITTED || assignment.status === ribbonService.assignmentStatus.SUBMITTED_LOCKED"></div>
                <div class="lock-assignment-icon" ng-if="assignment.status === ribbonService.assignmentStatus.LOCKED_PAST_DUE || assignment.status === ribbonService.assignmentStatus.LOCKED_FUTURE"></div>
                <p class="card-disp" ng-class="{'submitted-disp' : assignment.status === ribbonService.assignmentStatus.SUBMITTED || assignment.status === ribbonService.assignmentStatus.SUBMITTED_LOCKED }">{{ assignment.generated_status }}</p>
              </div>
            </div>
          </div>

          <div class="assignment-details skeleton-container" ng-if="ribbonService.skeletonLoad.assignmentDetail">
            <div class="details-head">
            </div>
            <div class="assignment-discription">
            </div>
            <div class="assignment-files-section">
            </div>
            <div class="submission-files-section">
              <div class="section-title">
              </div>
              <div class="submission-files" ng-repeat="n in [].constructor(4) track by $index">
              </div>
            </div>
            <div class="skeleton-table">
              <div class="skeleton-table-cells" ng-repeat="n in [].constructor(8) track by $index"></div>
            </div>
          </div>

          <div class="assignment-details" ng-show="!ribbonService.skeletonLoad.assignmentDetail && ribbonService.assignmentList && ribbonService.assignmentList.length > 0">
            <div class="details-head">
              <p class="title">{{ $root.selectedAssignment.name }}</p>
              <p class="assignment-info" ng-class="{'submitted-info' : $root.selectedAssignment.status === ribbonService.assignmentStatus.SUBMITTED || $root.selectedAssignment.status === ribbonService.assignmentStatus.SUBMITTED_LOCKED}">{{ $root.selectedAssignment.generated_status }}</p>
            </div>
            <div class="assignment-discription">
              <button ng-if="$root.selectedAssignment.description && $root.selectedAssignment.description.trim().length > 0" class="secondary-btn" ng-click="downloadDescription()">{{ 'ASSIGNMENT.DOWNLOAD_DESCRIPTION' | translate}}</button>
              <p ng-if="$root.selectedAssignment.description && $root.selectedAssignment.description.trim().length == 0" class="description-unavailable">No description provided.</p>
            </div>
            <div class="assignment-files-section">
              <p class="section-title">{{ $root.selectedAssignment.attachment_info}}</p>
              <button ng-if="$root.selectedAssignment.assignment_files_count && $root.selectedAssignment.assignment_files_count > 0" class="secondary-btn" ng-click="downloadAllFiles($root.selectedAssignment)">{{ 'ASSIGNMENT.DOWNLOAD_ATTACHMENT' | translate}}</button>
            </div>
            <div ng-if="$root.selectedAssignment.has_submitted_submissions" class="submission-files-section">
              <p class="section-title">{{ $root.selectedAssignment.submission_text }}</p>
              <div class="submission-files" ng-repeat="submittedFile in $root.selectedAssignment.submitted_files">
                <p class="file-name">{{ submittedFile.display_name }}</p>
              </div> 
            </div>
            <div ng-if="$root.selectedAssignment.status === ribbonService.assignmentStatus.UNLOCKED" class="submit-assignment-section">
              <p class="section-title">{{ 'ASSIGNMENT.SUBMIT_INSTRUCTION' | translate}}</p>
              <p class="submit-assignment-instruction">{{ 'ASSIGNMENT.SELECT_INSTRUCTION' | translate}}</p>
            </div>
            <div ng-if="$root.selectedAssignment.status === ribbonService.assignmentStatus.SUBMITTED && ribbonService.licenses.ltiIssuer !== ribbonService.LTI_ISSUER.D2L" class="submit-assignment-section">
              <button ng-if="canShowResubmitButton($root.selectedAssignment) && !$root.resubmitEnabled && $root.selectedAssignment.attemptAllowed" ng-click="$root.enableResubmit()" class="primary-btn">{{ 'ASSIGNMENT.BUTTON_RESUBMIT' | translate}}</button>
              <p ng-if="$root.resubmitEnabled" class="section-title">{{ 'ASSIGNMENT.SUBMIT_INSTRUCTION' | translate}}</p>
              <p ng-if="$root.resubmitEnabled" class="submit-assignment-instruction">{{ 'ASSIGNMENT.RESUBMIT_INSTRUCTION' | translate}}</p>
              <p ng-if="!$root.selectedAssignment.attemptAllowed" class="submit-assignment-instruction">{{ 'ASSIGNMENT.LIMIT_INFO' | translate}}</p>
            </div>
            <div ng-if="$root.selectedAssignment.status === ribbonService.assignmentStatus.LOCKED_PAST_DUE || $root.selectedAssignment.status === ribbonService.assignmentStatus.SUBMITTED_LOCKED" class="submit-assignment-section">
              <p ng-if="$root.selectedAssignment.status === ribbonService.assignmentStatus.LOCKED_PAST_DUE" class="section-title">{{ 'ASSIGNMENT.SUBMIT_INSTRUCTION' | translate}}</p>
              <p class="submit-assignment-instruction">{{ 'ASSIGNMENT.LOCKED_PAST_DUE' | translate}}</p>
            </div>
            <div ng-if="$root.selectedAssignment.status === ribbonService.assignmentStatus.LOCKED_FUTURE" class="submit-assignment-section">
              <p class="section-title">{{ 'ASSIGNMENT.SUBMIT_INSTRUCTION' | translate}}</p>
              <p class="submit-assignment-instruction">{{ 'ASSIGNMENT.LOCKED_FUTURE' | translate}}</p>
            </div>
            <assignment-management-files ng-if="ribbonService.assignmentManagementDialogVisible" ng-show="!ribbonService.skeletonLoad.assignmentDetail && ($root.selectedAssignment.status === ribbonService.assignmentStatus.UNLOCKED || $root.selectedAssignment.status === ribbonService.assignmentStatus.SUBMITTED && $root.resubmitEnabled)" multi-select="true" ></assignment-management-files>
          </div>
        </div>
      </div>
    </div>
  </div>