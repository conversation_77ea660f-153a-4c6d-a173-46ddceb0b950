/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.notification {
    border: 0px solid rgba(0, 0, 0, 0.125);
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.125);
    background: white;
    color: black;
    position: relative;
}


.notification.error {
    background: #FDD;
}

.notification .body {
    margin: 0.5em;
}

.notification-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-left {
    padding-left: 28px;
    margin-bottom: 32px;
}

.footer-right {
    padding-right: 28px;
    margin-bottom: 32px;
}

@keyframes notification-progress {
    from {background-position: 0px  0px;}
    to   {background-position: 64px 0px;}
}

@-webkit-keyframes notification-progress {
    from {background-position: 0px  0px;}
    to   {background-position: 64px 0px;}
}

.notification .title-bar {
    font-size: 24px;
    font-weight: bold;
}

.add_padding{
    padding: 32px;
    width: 558px !important;
    max-width: 558px !important;
    height: max-content;
}

.share_btns{
    text-align: left !important;
    padding-bottom: 0px !important;
    margin: 0px !important;
}

.confirm_text{
    margin-bottom: 24px !important;
    font-size: 24px;
    color: #1A1A1A;
    font-weight: 500;
    display: inline !important;
}

#share_screen_username{
    font-size: 24px !important;
    font-weight: 600;
    display: inline;
    color: #1A1A1A;
}

#share_screen_btn{
    background: #22538F;
}

#share_close_btn{
    background: #EDEBEB !important;
    color: #1A1A1A !important;
}

#share_dialog_body{
    margin: 0px !important;
    padding-top: 0px !important;
    padding-bottom: 24px !important;
}

#share_checkbox{
    width: 20px;
    height: 20px;
}

#share_close_icon{
    filter: brightness(0.5);
    position: absolute;
    right: 16px;
    top: 16px;
}

#share_confirm_dialog::before{
    background: none !important;
}

.notification .title-bar .title {
    padding-top: 32px;
    padding-left: 32px;
    padding-right: 32px;
    font-size: 24px;
}

.notification .sparkle-title {
    display: grid;
    grid-template-columns: auto 1fr;
}

.notification .sparkle-title .title-icon {
    padding-top: 32px;
    padding-left: 32px;
}

.notification .sparkle-title .sparkle-icon {
    background-image: url(app/ext/ribbon/images/ai-icon.svg);
    background-repeat: no-repeat;
    height: 30px;
    width: 30px;
}

.notification .sparkle-title .title {
    padding-bottom: 0px;
    padding-left: 10px;
}

.notification .progress .bar {
    background: #A3D655;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    box-shadow: inset  1px  1px 0 rgba(255, 255, 255, 0.5),
                inset -1px -1px 0 rgba(  0,   0,   0, 0.1),
                       1px 1px  0 gray;
}

.notification .progress {

    width: 100%;
    background: #C2C2C2 url('images/progress.png');
    background-size: 16px 16px;
    -moz-background-size: 16px 16px;
    -webkit-background-size: 16px 16px;
    -khtml-background-size: 16px 16px;

    animation-name: notification-progress;
    animation-duration: 2s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;

    -webkit-animation-name: notification-progress;
    -webkit-animation-duration: 2s;
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;

    padding: 0.25em;
    
    border: 1px solid gray;

    position: relative;
    
}

.notification .progress .text {
    position: relative;
}

.notification .parameters {
    width: 100%;
}

.notification .parameters .fields {
    display: table;
    width: 100%;
}

.notification .parameters .fields .labeled-field {
    display: table-row;
}

.notification .parameters .fields .field-header,
.notification .parameters .fields .form-field {
    text-align: left;
    display: table-cell;
    padding: .125em;
    vertical-align: top;
}

.notification .parameters .fields .field-header {
    padding-right: 1em;
}

.notification .parameters .fields .field-header {
    width: 0;
}

.notification .parameters .fields .form-field {
    width: 100%;
}

.notification .parameters input[type=text],
.notification .parameters input[type=email],
.notification .parameters input[type=number],
.notification .parameters input[type=password],
.notification .parameters textarea {
    max-width: 100%;
}
